"use client";

import { getAllCookies } from "../../../../shared/lib/cookies/crud/get";
import { CookieHeaderService } from "../../../../shared/lib/cookies/services/cookie-header.service";
import { IChatStreamRequest, IStreamingOptions } from "../../types/streaming.type";
import { StreamErrorHandler } from "./error-handler.service";
import { StreamProcessor } from "./streaming-processor";
import { CHAT_ENDPOINTS } from "../../api/endpoints";
import { ChatRetryStrategyService } from "../retry/retry-strategy.service";

interface IStreamingConfig {
	readonly chunkTimeout: number;
	readonly maxRetries: number;
	readonly retryDelay: number;
}

type IStreamingHeaders = Record<string, string> & {
	readonly "Content-Type": string;
	readonly Accept: string;
	readonly "Cache-Control": string;
	readonly Connection: string;
	readonly Cookie?: string;
};

export class StreamRequestHandler {
	private readonly config: IStreamingConfig = {
		chunkTimeout: 30000,
		maxRetries: 3,
		retryDelay: 1000,
	};

	private readonly retryService = new ChatRetryStrategyService();

	constructor(
		private readonly processor: StreamProcessor,
		private readonly errorHandler: StreamErrorHandler,
		private readonly cookieService: CookieHeaderService = new CookieHeaderService(),
	) {}

	public async handle(request: IChatStreamRequest, options: IStreamingOptions, signal: AbortSignal): Promise<void> {
		const operation = async () => {
			await this.executeStreamingRequest(request, options, signal);
		};

		// Create a strategy for network/streaming errors
		const strategy = this.retryService.createStrategy("network");

		const result = await this.retryService.executeWithRetry(
			operation,
			{
				...strategy,
				shouldRetry: (error, context) => {
					// Don't retry if aborted
					if (signal.aborted) return false;
					return strategy.shouldRetry(error, context);
				},
				onRetryAttempt: context => {
					console.debug(`Streaming retry attempt ${context.attempt}`, context.lastError);
				},
				onRetryExhausted: context => {
					const finalError = this.errorHandler.create(context.lastError, `Falha após ${context.attempt} tentativas`, {
						endpoint: CHAT_ENDPOINTS.ASK,
						retryAttempt: context.attempt,
						maxRetries: this.config.maxRetries,
					});
					options.onError?.(finalError);
				},
			},
			{
				maxRetries: this.config.maxRetries,
				baseDelay: this.config.retryDelay,
			},
		);

		if (!result.success && result.finalError) {
			throw new Error(result.finalError.message);
		}
	}

	private async executeStreamingRequest(request: IChatStreamRequest, options: IStreamingOptions, signal: AbortSignal): Promise<void> {
		const timeoutId = this.setupTimeout(signal);

		try {
			const headers = await this.buildRequestHeaders();
			const response = await this.makeHttpRequest(request, headers, signal);
			await this.validateAndProcessResponse(response, options);
		} finally {
			clearTimeout(timeoutId);
		}
	}

	private setupTimeout(signal: AbortSignal): NodeJS.Timeout {
		return setTimeout(() => signal?.throwIfAborted?.(), this.config.chunkTimeout);
	}

	private async buildRequestHeaders(): Promise<IStreamingHeaders> {
		const baseHeaders: IStreamingHeaders = {
			"Content-Type": "application/json",
			Accept: "text/event-stream",
			"Cache-Control": "no-cache",
			Connection: "keep-alive",
		};

		try {
			const cookiesData = await getAllCookies();
			const cookieHeader = this.cookieService.processCookieData(cookiesData);
			if (cookieHeader?.trim()) return { ...baseHeaders, Cookie: cookieHeader };
		} catch (error) {
			console.warn("Erro ao obter cookies para streaming:", error);
		}

		return baseHeaders;
	}

	private async makeHttpRequest(request: IChatStreamRequest, headers: IStreamingHeaders, signal: AbortSignal): Promise<Response> {
		return fetch(CHAT_ENDPOINTS.ASK, {
			method: "POST",
			headers,
			body: JSON.stringify(request),
			signal,
			mode: "cors",
		});
	}

	private async validateAndProcessResponse(response: Response, options: IStreamingOptions): Promise<void> {
		if (!response.ok) this.errorHandler.throwHttp(response);
		if (!response.body) throw new Error("Nenhuma resposta recebida do servidor");
		await this.processor.process(response.body, options);
	}
}
