// Mock jotai
jest.mock("jotai", () => ({
	atom: jest.fn(initialValue => ({ init: initialValue })),
	useAtomValue: jest.fn(),
	useSetAtom: jest.fn(),
}));

import { fireEvent, render, screen } from "@testing-library/react";
import { useAtomValue } from "jotai";
import { ChatSidebar } from "../../components/sidebar/container";
import { useSessionManager } from "../../hooks/session-manager.hook";
import { useStreamingManager } from "../../hooks/streaming-manager.hook";

// Mock hooks
jest.mock("../../hooks/session-manager.hook", () => ({
	useSessionManager: jest.fn(),
}));

jest.mock("../../hooks/streaming-manager.hook", () => ({
	useStreamingManager: jest.fn(),
}));

// Typed mocks
const useAtomValueMock = jest.mocked(useAtomValue);
const useSessionManagerMock = jest.mocked(useSessionManager);
const useStreamingManagerMock = jest.mocked(useStreamingManager);
jest.mock("../../components/sidebar/header", () => ({
	ChatHeader: () => <div data-testid="chat-header" />,
}));
jest.mock("../../components/sidebar/input", () => ({
	ChatInput: (props: { disabled: boolean; onSend: (message: string) => void }) => (
		<button data-testid="chat-input" disabled={props.disabled} onClick={() => props.onSend && props.onSend("test message")}>
			Send
		</button>
	),
}));
jest.mock("../../components/sidebar/content", () => ({
	ChatMessage: ({ message }: { message: { content: string } }) => <div data-testid="chat-message">{message.content}</div>,
}));

describe("ChatSidebar", () => {
	beforeEach(() => {
		jest.clearAllMocks();

		useSessionManagerMock.mockReturnValue({
			createNewSession: jest.fn(),
			removeSession: jest.fn(),
		});

		useStreamingManagerMock.mockReturnValue({
			sendMessage: jest.fn(),
			stop: jest.fn(),
			reset: jest.fn(),
			isStreaming: false,
			currentMessageId: null,
			canSend: true,
			canStop: false,
			canResume: false,
		});
	});

	it("does not render sidebar when isOpen is false", () => {
		useAtomValueMock.mockReturnValueOnce(false); // isOpen

		render(<ChatSidebar />);
		expect(screen.queryByTestId("chat-header")).not.toBeInTheDocument();
		expect(screen.queryByTestId("chat-input")).not.toBeInTheDocument();
	});

	it("renders welcome message when no messages are available", () => {
		useAtomValueMock
			.mockReturnValueOnce(true) // isOpen
			.mockReturnValueOnce(false) // hasMessages
			.mockReturnValueOnce([]); // messages

		render(<ChatSidebar />);
		expect(screen.getByText("Bem-vindo à Doorinha")).toBeInTheDocument();
		expect(screen.getByText(/Faça perguntas/)).toBeInTheDocument();
		expect(screen.getByTestId("chat-header")).toBeInTheDocument();
		expect(screen.getByTestId("chat-input")).toBeInTheDocument();
	});

	it("renders messages when available", () => {
		const messages = [
			{ id: "1", content: "Hello" },
			{ id: "2", content: "World" },
		];
		useAtomValueMock
			.mockReturnValueOnce(true) // isOpen
			.mockReturnValueOnce(true) // hasMessages
			.mockReturnValueOnce(messages); // messages

		render(<ChatSidebar />);
		expect(screen.getByTestId("chat-header")).toBeInTheDocument();
		expect(screen.getByTestId("chat-input")).toBeInTheDocument();
		expect(screen.getAllByTestId("chat-message")).toHaveLength(2);
		expect(screen.getByText("Hello")).toBeInTheDocument();
		expect(screen.getByText("World")).toBeInTheDocument();
	});

	it("calls sendMessage when ChatInput is clicked", () => {
		const sendMessage = jest.fn();
		useStreamingManagerMock.mockReturnValue({
			sendMessage,
			stop: jest.fn(),
			reset: jest.fn(),
			isStreaming: false,
			currentMessageId: null,
			canSend: true,
			canStop: false,
			canResume: false,
		});
		useAtomValueMock
			.mockReturnValueOnce(true) // isOpen
			.mockReturnValueOnce(true) // hasMessages
			.mockReturnValueOnce([]); // messages

		render(<ChatSidebar />);
		fireEvent.click(screen.getByTestId("chat-input"));
		expect(sendMessage).toHaveBeenCalledWith("test message");
	});

	it("passes isStreaming to ChatInput", () => {
		useStreamingManagerMock.mockReturnValue({
			sendMessage: jest.fn(),
			stop: jest.fn(),
			reset: jest.fn(),
			isStreaming: true,
			currentMessageId: null,
			canSend: false,
			canStop: true,
			canResume: true,
		});
		useAtomValueMock
			.mockReturnValueOnce(true) // isOpen
			.mockReturnValueOnce(true) // hasMessages
			.mockReturnValueOnce([]); // messages

		render(<ChatSidebar />);
		expect(screen.getByTestId("chat-input")).toBeInTheDocument();
	});
});
