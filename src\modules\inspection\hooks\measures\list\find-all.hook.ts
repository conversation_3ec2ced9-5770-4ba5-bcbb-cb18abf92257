import { MEASURES_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createGetRequest } from "@/shared/lib/requests";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export interface IMeasuresDto {
	id: string;
	name: string;
	abbreviation: string;
}

export interface IFindAllMeasureaParamns {
	page?: number;
	limit?: number;
	search?: string;
}

export default function useFindAllMeasures({ page = 1, limit = 10, search = "" }: IFindAllMeasureaParamns) {
	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.measures.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IMeasuresDto>>(MEASURES_ENDPOINTS.FIND_ALL({ page, limit, search })),
	});

	const isNoDataFound = !data?.success && data?.status === 404;

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
}
