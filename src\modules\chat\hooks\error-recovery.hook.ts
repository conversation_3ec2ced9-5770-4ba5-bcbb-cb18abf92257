"use client";

import { use<PERSON><PERSON>Value, useSet<PERSON><PERSON> } from "jotai";
import { useCallback, useRef } from "react";
import {
	chatError<PERSON>tom,
	chatIsRecoveringAtom,
	chatRecoveryMessageAtom,
	incrementRetryCountAtom,
	resetError<PERSON>tate<PERSON>tom,
} from "../atoms/handlers/error.atom";
import { ChatAutoRecoveryService, IAutoRecoveryDependencies } from "../services/recovery/auto-recovery.service";
import { ChatRetryStrategyService } from "../services/retry/retry-strategy.service";
import { ChatErrorClassifierService } from "../services/error-classification/error-classifier.service";
import { IChatError, IHandleErrorRecoveryScope } from "../types/handlers.type";

interface IUseErrorRecoveryHookScope {
	readonly handleError: (error: unknown, context?: Partial<IChatError["context"]>) => Promise<boolean>;
	readonly retryOperation: <T>(operation: () => Promise<T>) => Promise<T>;
	readonly resetError: () => void;
	readonly isRecovering: boolean;
	readonly currentError: IChatError | null;
}

interface IUseErrorRecoveryOptions {
	readonly dependencies?: IAutoRecoveryDependencies;
	readonly recoveryScope?: IHandleErrorRecoveryScope;
	readonly onRecoverySuccess?: () => void;
	readonly onRecoveryFailure?: (error: IChatError) => void;
	readonly maxRetryAttempts?: number;
}

export const useErrorRecovery = (options: IUseErrorRecoveryOptions = {}): IUseErrorRecoveryHookScope => {
	const setError = useSetAtom(chatErrorAtom);
	const setIsRecovering = useSetAtom(chatIsRecoveringAtom);
	const setRecoveryMessage = useSetAtom(chatRecoveryMessageAtom);
	const incrementRetryCount = useSetAtom(incrementRetryCountAtom);
	const resetErrorState = useSetAtom(resetErrorStateAtom);
	
	const currentError = useAtomValue(chatErrorAtom);
	const isRecovering = useAtomValue(chatIsRecoveringAtom);

	// Services
	const classifierRef = useRef(new ChatErrorClassifierService());
	const retryServiceRef = useRef(new ChatRetryStrategyService());
	const recoveryServiceRef = useRef(new ChatAutoRecoveryService(options.dependencies || {}));

	const handleError = useCallback(
		async (error: unknown, context: Partial<IChatError["context"]> = {}): Promise<boolean> => {
			try {
				// Classify the error
				const classifiedError = classifierRef.current.classify({
					error,
					context,
					httpStatus: context.httpStatus,
				});

				setError(classifiedError);

				// If error is not recoverable, show it to user immediately
				if (!classifiedError.isRecoverable) {
					options.onRecoveryFailure?.(classifiedError);
					return false;
				}

				// Attempt automatic recovery
				setIsRecovering(true);
				setRecoveryMessage(classifiedError.userMessage);

				const recoveryResult = await recoveryServiceRef.current.attemptRecovery(
					classifiedError,
					options.recoveryScope
				);

				if (recoveryResult.success && recoveryResult.shouldRetryOriginalOperation) {
					// Recovery successful, clear error state
					resetErrorState();
					options.onRecoverySuccess?.();
					return true;
				} else {
					// Recovery failed, show error to user
					setIsRecovering(false);
					setRecoveryMessage(null);
					setError({
						...classifiedError,
						userMessage: recoveryResult.message,
						isRecoverable: false,
					});
					options.onRecoveryFailure?.(classifiedError);
					return false;
				}
			} catch (recoveryError) {
				console.error("Error in recovery process:", recoveryError);
				setIsRecovering(false);
				setRecoveryMessage(null);
				
				// Create a generic error for the recovery failure
				const fallbackError = classifierRef.current.classify({
					error: recoveryError,
					context,
				});
				
				setError(fallbackError);
				options.onRecoveryFailure?.(fallbackError);
				return false;
			}
		},
		[
			setError,
			setIsRecovering,
			setRecoveryMessage,
			resetErrorState,
			options.recoveryScope,
			options.onRecoverySuccess,
			options.onRecoveryFailure,
		]
	);

	const retryOperation = useCallback(
		async <T>(operation: () => Promise<T>): Promise<T> => {
			if (!currentError) {
				return await operation();
			}

			const strategy = retryServiceRef.current.createStrategy(currentError.type);
			const maxRetries = options.maxRetryAttempts || 3;

			const result = await retryServiceRef.current.executeWithRetry(
				operation,
				{
					...strategy,
					onRetryAttempt: (context) => {
						incrementRetryCount();
						setRecoveryMessage(`Tentativa ${context.attempt} de ${context.totalAttempts}...`);
						strategy.onRetryAttempt?.(context);
					},
					onRetryExhausted: (context) => {
						setIsRecovering(false);
						setRecoveryMessage(null);
						strategy.onRetryExhausted?.(context);
					},
				},
				{ maxRetries }
			);

			if (result.success && result.data !== undefined) {
				resetErrorState();
				return result.data;
			} else {
				if (result.finalError) {
					setError(result.finalError);
				}
				throw new Error(result.finalError?.userMessage || "Operação falhou após múltiplas tentativas");
			}
		},
		[
			currentError,
			options.maxRetryAttempts,
			incrementRetryCount,
			setRecoveryMessage,
			setIsRecovering,
			resetErrorState,
			setError,
		]
	);

	const resetError = useCallback(() => {
		resetErrorState();
	}, [resetErrorState]);

	return {
		handleError,
		retryOperation,
		resetError,
		isRecovering,
		currentError,
	};
};
