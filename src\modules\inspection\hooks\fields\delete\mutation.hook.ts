import { toast } from "@/core/toast";
import { FIELDS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export function useDeleteFieldMutation() {
	const queryClient = useQueryClient();
	const deleteFields = useMutation({
		mutationKey: inspectionKeys.fields.custom("delete"),
		mutationFn: async (id: string) => {
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(FIELDS_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.fields.invalidateAll(queryClient),
	});

	return {
		deleteField: (id: string) =>
			toast.promise(deleteFields.mutateAsync(id), {
				loading: "Excluindo campo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
}
