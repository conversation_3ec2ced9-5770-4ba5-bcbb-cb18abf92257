import { toast } from "@/core/toast";
import { COLLAB_BY_SECTOR_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { inspectionKeys } from "@/modules/inspection/constants/query/keys";
import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useDeleteCollabBySectorMutation() {
	const queryClient = useQueryClient();
	const deleteCollabBySectorMutation = useMutation({
		mutationKey: inspectionKeys.collabBysector.custom("delete"),
		mutationFn: async (id: string) => {
			const res = await createDeleteRequest<IMessageGlobalReturn>(COLLAB_BY_SECTOR_ENDPOINTS.DELETE(id));
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => inspectionKeys.collabBysector.invalidateAllLists(queryClient),
	});

	return {
		deleteCollabBySector: (id: string) =>
			toast.promise(deleteCollabBySectorMutation.mutateAsync(id), {
				loading: "Deletando vinculo de colaborador por setor...",
				success: data => data.message ?? "Vinculo deletado com sucesso!",
				error: data => data.message ?? "Erro ao excluir o vinculo",
			}),
	};
}
