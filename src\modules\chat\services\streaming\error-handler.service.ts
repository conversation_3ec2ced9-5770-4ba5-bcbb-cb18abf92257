import { IChatError } from "../../types/handlers.type";
import { ChatErrorClassifierService } from "../error-classification/error-classifier.service";

export class StreamErrorHandler {
	private readonly classifier = new ChatErrorClassifierService();

	async throwHttp(response: Response): Promise<never> {
		let errorMessage = `Erro HTTP ${response.status}`;
		let responseText: string | undefined;

		try {
			responseText = await response.text();
			if (responseText) errorMessage += ` - ${responseText}`;
		} catch {
			errorMessage += " - Não foi possível ler a mensagem de erro";
		}

		const httpError = new Error(errorMessage);
		// Add response status for better classification
		(httpError as any).status = response.status;
		(httpError as any).responseText = responseText;

		throw httpError;
	}

	create(error: unknown, customMessage?: string, context: Partial<IChatError["context"]> = {}): IChatError {
		// Use the new classifier for better error handling
		return this.classifier.classify({
			error,
			context,
			httpStatus: (error as any)?.status,
			responseText: (error as any)?.responseText,
		});
	}

	// Legacy method for backward compatibility
	createLegacy(error: unknown, customMessage?: string): { message: string; details?: unknown } {
		return {
			message: customMessage || (error instanceof Error ? error.message : "Erro desconhecido no streaming"),
			details: error,
		};
	}
}
