import { chatStreamService } from "../services/streaming/streaming.service";
import { IChatError } from "../types/handlers.type";
import { IChatMessage } from "../types/messages.type";
import { IChatStreamRequest } from "../types/streaming.type";
import { handleStreamingChunk } from "./handler-chunk.lib";

interface IStreamingMessageProps {
	sessionId: string;
	content: string;
	addMessage: (message: Omit<IChatMessage, "id" | "timestamp">) => string;
	onUpdate: (messageId: string, updates: Partial<IChatMessage>) => void;
	setError: (error: string | null) => Promise<void>;
	onStreamingStart?: (messageId: string) => void;
}

export const sendStreamingMessage = async ({ sessionId, content, addMessage, onUpdate, setError, onStreamingStart }: IStreamingMessageProps) => {
	await setError(null);
	addMessage({
		content: content.trim(),
		role: "user",
	});

	const assistantMessage = addMessage({
		content: "",
		role: "assistant",
	});

	if (!assistantMessage) throw new Error("Ocorreu um erro ao criar a mensagem do assistente. [CLIENT-ID-CREATOR]");

	onStreamingStart?.(assistantMessage);

	const request: IChatStreamRequest = {
		sessionId,
		message: content.trim(),
	};

	let accumulatedContent = "";

	await chatStreamService.streamChat(request, {
		onChunk: chunkData => {
			if (chunkData.content !== undefined) {
				accumulatedContent += chunkData.content;
				handleStreamingChunk({
					chunkData,
					updateMessage: onUpdate,
					assistantMessageId: assistantMessage,
					currentValue: accumulatedContent,
				});
			}
		},
		onError: async (error: IChatError) => {
			await setError(error.message || "Erro ao processar resposta");
			onUpdate(assistantMessage, {
				content: error.message || "Erro ao processar resposta",
				isError: true,
			});
		},
	});
};
