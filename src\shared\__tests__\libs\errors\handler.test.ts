import { DEFAULT_ERROR_MESSAGES, handleGlobalError } from "@/shared/lib/errors";
import { AxiosError, AxiosHeaderValue, AxiosRequestHeaders } from "axios";

function makeAxiosError(opts: { message?: string; status?: number | null; data?: unknown; method?: string; url?: string }): AxiosError {
	const err = new Error(opts.message ?? "axios error");
	Object.setPrototypeOf(err, AxiosError.prototype);
	const axiosLike = err as unknown as AxiosError & {
		response?: { status: number | null; data: unknown } | undefined;
		config?: { method?: string | undefined; url?: string | undefined; headers?: Record<string, AxiosHeaderValue> | undefined };
	};
	axiosLike.response =
		opts.status === undefined && opts.data === undefined
			? undefined
			: ({ status: opts.status ?? null, data: opts.data } as unknown as AxiosError["response"]);
	axiosLike.config = { method: opts.method, url: opts.url, headers: {} as AxiosRequestHeaders };
	return axiosLike;
}

describe("handleGlobalError", () => {
	it("usa response.data.message quando for uma string", () => {
		const axiosErr = makeAxiosError({
			message: "original",
			status: 404,
			data: { message: "Not found" },
			method: "get",
			url: "/path",
		});
		const res = handleGlobalError(axiosErr);
		expect(res.success).toBe(false);
		expect(res.status).toBe(404);
		expect(res.data.message).toBe("Not found");
		expect(res.data.method).toBe("get");
		expect(res.data.url).toBe("/path");
		expect(res.data.details).toEqual({ message: "Not found" });
	});

	it("une mensagens de array em response.data.message", () => {
		const axiosErr = makeAxiosError({
			message: "orig",
			status: 400,
			data: { message: ["First", "", "Second"] },
		});
		const res = handleGlobalError(axiosErr);
		expect(res.status).toBe(400);
		expect(res.data.message).toBe("First. Second");
	});

	it("recai para response.data.error ou response.data.detail", () => {
		const errWithError = makeAxiosError({ status: 422, data: { error: "Err text" } });
		expect(handleGlobalError(errWithError).data.message).toBe("Err text");

		const errWithDetail = makeAxiosError({ status: 422, data: { detail: "Detail text" } });
		expect(handleGlobalError(errWithDetail).data.message).toBe("Detail text");
	});

	it("quando AxiosError não tem response usa error.message e status 0", () => {
		const axiosErr = makeAxiosError({ message: "Network fail", status: undefined, data: undefined });
		const res = handleGlobalError(axiosErr);
		expect(res.status).toBe(0);
		expect(res.data.message).toBe("Network fail");
	});

	it("lida com Error simples com status 500 e preserva a mensagem", () => {
		const e = new Error("boom");
		const res = handleGlobalError(e);
		expect(res.status).toBe(500);
		expect(res.data.message).toBe("boom");
		expect(res.data.method).toBeUndefined();
		expect(res.data.details).toBeUndefined();
	});

	it("lida com valores não-error retornando a mensagem padrão 500", () => {
		const res = handleGlobalError("some string value");
		expect(res.status).toBe(500);
		expect(res.data.message).toBe(DEFAULT_ERROR_MESSAGES[500]);
		expect(res.data.method).toBeUndefined();
		expect(res.data.details).toBeUndefined();
	});
});
