import { useUpdateFormMutation } from "@/modules/inspection/hooks/form/edit/mutation.hook";
import { createPutRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";

jest.mock("@/shared/lib/requests", () => ({
	createPutRequest: jest.fn(),
}));

jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

const mockedCreatePutRequest = createPutRequest as jest.MockedFunction<typeof createPutRequest>;

const mockSuccess: IMessageGlobalReturn = { message: "Formulário editado com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Error ao editar o formulário" };

describe("useUpdateFormMutation", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve editar o formulário com sucesso", async () => {
		mockedCreatePutRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useUpdateFormMutation(() => {}), { wrapper });
		await expect(
			result.current.updateForm({
				form: {
					title: "Test Title",
					nomenclature: "Test Nomenclature",
					developerId: "dev-1",
					approverId: "app-1",
					fields: [],
				},
				id: "123",
			}),
		).resolves.toEqual(mockSuccess);
	});

	it("deve falhar ao editar o formulário", async () => {
		mockedCreatePutRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useUpdateFormMutation(() => {}), { wrapper });
		await expect(
			result.current.updateForm({
				form: {
					title: "Test Title",
					nomenclature: "Test Nomenclature",
					developerId: "dev-1",
					approverId: "app-1",
					fields: [],
				},
				id: "123",
			}),
		).rejects.toThrow("Error ao editar o formulário");
	});
});
