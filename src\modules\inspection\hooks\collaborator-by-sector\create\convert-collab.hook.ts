import { useFindAllCollaborator } from "@/modules/collaborator/hooks/list/find-all.hook";
import { ICollaboratorDto } from "@/modules/collaborator/types/find-all.dto";
import { IFindAllFieldsParams } from "../../fields/list/find-all.hook";

export function useFindAllCollaboratorSelect(params: IFindAllFieldsParams) {
	const { data, ...rest } = useFindAllCollaborator(params);

	const mappedData =
		data?.map((item: ICollaboratorDto) => ({
			id: item.document,
			name: item.name,
		})) ?? [];

	return { data: mappedData, ...rest };
}
