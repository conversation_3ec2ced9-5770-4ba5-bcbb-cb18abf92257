import { ChatErrorClassifierService } from "../../services/error-classification/error-classifier.service";

describe("ChatErrorClassifierService", () => {
	let classifier: ChatErrorClassifierService;

	beforeEach(() => {
		classifier = new ChatErrorClassifierService();
	});

	describe("Network Errors", () => {
		it("should classify network error when offline", () => {
			// Mock navigator.onLine
			Object.defineProperty(navigator, "onLine", {
				writable: true,
				value: false,
			});

			const result = classifier.classify({
				error: new Error("Network error"),
				context: {},
			});

			expect(result.type).toBe("network");
			expect(result.severity).toBe("medium");
			expect(result.isRecoverable).toBe(true);
			expect(result.shouldRetry).toBe(true);
			expect(result.recoveryStrategy).toBe("retry");
		});

		it("should classify fetch error as network error", () => {
			const fetchError = new TypeError("Failed to fetch");

			const result = classifier.classify({
				error: fetchError,
				context: {},
			});

			expect(result.type).toBe("network");
			expect(result.isRecoverable).toBe(true);
			expect(result.shouldRetry).toBe(true);
		});
	});

	describe("HTTP Status Errors", () => {
		it("should classify 401 as authentication error", () => {
			const result = classifier.classify({
				error: new Error("Unauthorized"),
				context: {},
				httpStatus: 401,
			});

			expect(result.type).toBe("authentication");
			expect(result.severity).toBe("high");
			expect(result.recoveryStrategy).toBe("refresh-token");
			expect(result.isRecoverable).toBe(true);
			expect(result.shouldRetry).toBe(false);
		});

		it("should classify 403 as authorization error", () => {
			const result = classifier.classify({
				error: new Error("Forbidden"),
				context: {},
				httpStatus: 403,
			});

			expect(result.type).toBe("authorization");
			expect(result.severity).toBe("high");
			expect(result.recoveryStrategy).toBe("user-action");
			expect(result.isRecoverable).toBe(false);
			expect(result.shouldRetry).toBe(false);
		});

		it("should classify 429 as rate limit error", () => {
			const result = classifier.classify({
				error: new Error("Too Many Requests"),
				context: {},
				httpStatus: 429,
			});

			expect(result.type).toBe("rate-limit");
			expect(result.severity).toBe("low");
			expect(result.recoveryStrategy).toBe("retry");
			expect(result.isRecoverable).toBe(true);
			expect(result.shouldRetry).toBe(true);
		});

		it("should classify 500 as server error", () => {
			const result = classifier.classify({
				error: new Error("Internal Server Error"),
				context: {},
				httpStatus: 500,
			});

			expect(result.type).toBe("server-error");
			expect(result.severity).toBe("high");
			expect(result.recoveryStrategy).toBe("retry");
			expect(result.isRecoverable).toBe(true);
			expect(result.shouldRetry).toBe(true);
		});
	});

	describe("Session Errors", () => {
		it("should classify session expired error", () => {
			const result = classifier.classify({
				error: new Error("Session expired"),
				context: {},
				responseText: "Your session has expired",
			});

			expect(result.type).toBe("session-expired");
			expect(result.severity).toBe("medium");
			expect(result.recoveryStrategy).toBe("refresh-session");
			expect(result.isRecoverable).toBe(true);
			expect(result.shouldRetry).toBe(false);
		});
	});

	describe("Timeout Errors", () => {
		it("should classify timeout error", () => {
			const timeoutError = new Error("Request timeout");
			timeoutError.name = "TimeoutError";

			const result = classifier.classify({
				error: timeoutError,
				context: {},
			});

			expect(result.type).toBe("timeout");
			expect(result.severity).toBe("medium");
			expect(result.recoveryStrategy).toBe("retry");
			expect(result.isRecoverable).toBe(true);
			expect(result.shouldRetry).toBe(true);
		});

		it("should classify 408 as timeout error", () => {
			const result = classifier.classify({
				error: new Error("Request Timeout"),
				context: {},
				httpStatus: 408,
			});

			expect(result.type).toBe("timeout");
			expect(result.isRecoverable).toBe(true);
			expect(result.shouldRetry).toBe(true);
		});
	});

	describe("Generic Errors", () => {
		it("should classify unknown error as generic", () => {
			const result = classifier.classify({
				error: new Error("Unknown error"),
				context: {},
			});

			expect(result.type).toBe("generic");
			expect(result.severity).toBe("medium");
			expect(result.recoveryStrategy).toBe("none");
			expect(result.isRecoverable).toBe(false);
			expect(result.shouldRetry).toBe(false);
		});
	});

	describe("Error Context", () => {
		it("should include context information in classified error", () => {
			const context = {
				sessionId: "test-session",
				endpoint: "/api/chat",
				retryAttempt: 2,
				maxRetries: 3,
			};

			const result = classifier.classify({
				error: new Error("Test error"),
				context,
			});

			expect(result.context.sessionId).toBe("test-session");
			expect(result.context.endpoint).toBe("/api/chat");
			expect(result.context.retryAttempt).toBe(2);
			expect(result.context.maxRetries).toBe(3);
			expect(result.context.timestamp).toBeDefined();
		});

		it("should include network information in details", () => {
			const result = classifier.classify({
				error: new Error("Test error"),
				context: {},
			});

			expect(result.details?.networkInfo).toBeDefined();
			expect(result.details?.networkInfo?.online).toBeDefined();
		});
	});
});
