export type TChatErrorType =
	| "network"
	| "authentication"
	| "authorization"
	| "timeout"
	| "server-error"
	| "validation"
	| "session-expired"
	| "rate-limit"
	| "generic";

export type TChatErrorSeverity = "low" | "medium" | "high" | "critical";

export type TChatErrorRecoveryStrategy = "retry" | "refresh-token" | "refresh-session" | "user-action" | "none";

export interface IChatErrorContext {
	readonly timestamp: number;
	readonly sessionId?: string;
	readonly requestId?: string;
	readonly userAgent?: string;
	readonly endpoint?: string;
	readonly httpStatus?: number;
	readonly retryAttempt?: number;
	readonly maxRetries?: number;
}

export interface IChatErrorDetails {
	readonly originalError?: unknown;
	readonly stackTrace?: string;
	readonly requestPayload?: unknown;
	readonly responseData?: unknown;
	readonly networkInfo?: {
		readonly online: boolean;
		readonly connectionType?: string;
	};
}

export interface IChatError {
	readonly id: string;
	readonly type: TChatErrorType;
	readonly severity: TChatErrorSeverity;
	readonly message: string;
	readonly userMessage: string;
	readonly recoveryStrategy: TChatErrorRecoveryStrategy;
	readonly isRecoverable: boolean;
	readonly shouldRetry: boolean;
	readonly context: IChatErrorContext;
	readonly details?: IChatErrorDetails;
}

export interface IHandleErrorRecoveryScope {
	readonly onRetry?: () => Promise<void>;
	readonly onRefreshToken?: () => Promise<void>;
	readonly onRefreshSession?: () => Promise<void>;
	readonly onUserAction?: (action: string) => Promise<void>;
}
