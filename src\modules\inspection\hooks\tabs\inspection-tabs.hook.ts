"use client";

import { useAtom } from "jotai";
import { useCallback } from "react";
import { activeTabsAtom } from "../../atoms/active-tabs.atom";
export const TABS = ["medidas", "campos", "vinculo-colaboradores", "formularios", "tipo-produto", "componentes", "vinculos"] as const;

export type TInspectionTabValue = (typeof TABS)[number];

export interface IUseInspectionTabsReturn {
	activeTab: TInspectionTabValue;
	setActiveTab: (tab: TInspectionTabValue) => void;
	isTabActive: (tab: TInspectionTabValue) => boolean;
	goToNextTab: () => void;
	goToPreviousTab: () => void;
	availableTabs: readonly TInspectionTabValue[];
}

export const useInspectionTabs = (): IUseInspectionTabsReturn => {
	const [activeTab, setActiveTab] = useAtom(activeTabsAtom);

	const isTabActive = useCallback((tab: TInspectionTabValue) => activeTab === tab, [activeTab]);

	const goToNextTab = useCallback(() => {
		const idx = TABS.indexOf(activeTab);
		setActiveTab(TABS[(idx + 1) % TABS.length]);
	}, [activeTab, setActiveTab]);

	const goToPreviousTab = useCallback(() => {
		const idx = TABS.indexOf(activeTab);
		setActiveTab(TABS[(idx - 1 + TABS.length) % TABS.length]);
	}, [activeTab, setActiveTab]);

	return {
		activeTab,
		setActiveTab,
		isTabActive,
		goToNextTab,
		goToPreviousTab,
		availableTabs: TABS,
	};
};
