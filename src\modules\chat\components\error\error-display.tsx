"use client";

import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { AlertCircle, RefreshCw, Wifi, WifiOff } from "lucide-react";
import { But<PERSON> } from "../../../../shared/components/shadcn/button";
import { cn } from "../../../../shared/lib/shadcn/utils";
import { chatErrorDisplayMessageAtom, chatIsRecoveringAtom, chatShouldShowErrorAtom } from "../../atoms/handlers/error.atom";
import { TChatErrorType } from "../../types/handlers.type";

interface IChatErrorDisplayProps {
	readonly onRetry?: () => void;
	readonly onDismiss?: () => void;
	readonly className?: string;
}

interface IErrorDisplayConfig {
	readonly icon: React.ComponentType<{ className?: string }>;
	readonly bgColor: string;
	readonly borderColor: string;
	readonly textColor: string;
	readonly iconColor: string;
}

const errorDisplayConfigs: Record<TChatErrorType, IErrorDisplayConfig> = {
	network: {
		icon: WifiOff,
		bgColor: "bg-orange-50/90",
		borderColor: "border-orange-200/60",
		textColor: "text-orange-800",
		iconColor: "text-orange-600",
	},
	authentication: {
		icon: RefreshCw,
		bgColor: "bg-blue-50/90",
		borderColor: "border-blue-200/60",
		textColor: "text-blue-800",
		iconColor: "text-blue-600",
	},
	authorization: {
		icon: AlertCircle,
		bgColor: "bg-red-50/90",
		borderColor: "border-red-200/60",
		textColor: "text-red-800",
		iconColor: "text-red-600",
	},
	timeout: {
		icon: RefreshCw,
		bgColor: "bg-yellow-50/90",
		borderColor: "border-yellow-200/60",
		textColor: "text-yellow-800",
		iconColor: "text-yellow-600",
	},
	"server-error": {
		icon: AlertCircle,
		bgColor: "bg-red-50/90",
		borderColor: "border-red-200/60",
		textColor: "text-red-800",
		iconColor: "text-red-600",
	},
	validation: {
		icon: AlertCircle,
		bgColor: "bg-amber-50/90",
		borderColor: "border-amber-200/60",
		textColor: "text-amber-800",
		iconColor: "text-amber-600",
	},
	"session-expired": {
		icon: RefreshCw,
		bgColor: "bg-blue-50/90",
		borderColor: "border-blue-200/60",
		textColor: "text-blue-800",
		iconColor: "text-blue-600",
	},
	"rate-limit": {
		icon: RefreshCw,
		bgColor: "bg-purple-50/90",
		borderColor: "border-purple-200/60",
		textColor: "text-purple-800",
		iconColor: "text-purple-600",
	},
	generic: {
		icon: AlertCircle,
		bgColor: "bg-gray-50/90",
		borderColor: "border-gray-200/60",
		textColor: "text-gray-800",
		iconColor: "text-gray-600",
	},
};

export const ChatErrorDisplay = ({ onRetry, onDismiss, className }: IChatErrorDisplayProps) => {
	const shouldShow = useAtomValue(chatShouldShowErrorAtom);
	const isRecovering = useAtomValue(chatIsRecoveringAtom);
	const displayMessage = useAtomValue(chatErrorDisplayMessageAtom);

	if (!shouldShow && !isRecovering) return null;

	const config = errorDisplayConfigs.generic; // Default config for now
	const IconComponent = config.icon;

	return (
		<AnimatePresence>
			{(shouldShow || isRecovering) && (
				<motion.div
					initial={{ opacity: 0, y: 10, scale: 0.95 }}
					animate={{ opacity: 1, y: 0, scale: 1 }}
					exit={{ opacity: 0, y: -10, scale: 0.95 }}
					transition={{ duration: 0.2, ease: "easeOut" }}
					className={cn(
						"mx-4 mb-3 rounded-xl border-2 border-dashed p-4 backdrop-blur-sm transition-all duration-200",
						config.bgColor,
						config.borderColor,
						className
					)}
					role="alert"
					aria-live="polite"
				>
					<div className="flex items-start gap-3">
						<div className="flex-shrink-0">
							{isRecovering ? (
								<RefreshCw className={cn("h-5 w-5 animate-spin", config.iconColor)} />
							) : (
								<IconComponent className={cn("h-5 w-5", config.iconColor)} />
							)}
						</div>

						<div className="flex-1 min-w-0">
							<p className={cn("text-sm font-medium leading-relaxed", config.textColor)}>
								{displayMessage || "Ocorreu um erro inesperado"}
							</p>
						</div>

						{!isRecovering && (onRetry || onDismiss) && (
							<div className="flex gap-2">
								{onRetry && (
									<Button
										onClick={onRetry}
										variant="outline"
										size="sm"
										className={cn(
											"h-8 px-3 text-xs font-medium transition-all duration-200",
											"hover:scale-105 focus:scale-105",
											config.textColor,
											"border-current/20 hover:bg-current/10"
										)}
									>
										<RefreshCw className="mr-1 h-3 w-3" />
										Tentar Novamente
									</Button>
								)}
								{onDismiss && (
									<Button
										onClick={onDismiss}
										variant="ghost"
										size="sm"
										className={cn(
											"h-8 px-2 text-xs transition-all duration-200",
											"hover:scale-105 focus:scale-105",
											config.textColor,
											"hover:bg-current/10"
										)}
									>
										×
									</Button>
								)}
							</div>
						)}
					</div>

					{isRecovering && (
						<div className="mt-3">
							<div className={cn("h-1 w-full rounded-full", config.bgColor)}>
								<motion.div
									className={cn("h-full rounded-full", config.iconColor.replace("text-", "bg-"))}
									initial={{ width: "0%" }}
									animate={{ width: "100%" }}
									transition={{ duration: 2, ease: "easeInOut", repeat: Infinity }}
								/>
							</div>
						</div>
					)}
				</motion.div>
			)}
		</AnimatePresence>
	);
};
