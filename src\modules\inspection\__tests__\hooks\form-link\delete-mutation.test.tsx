import { useDeleteFieldMutation } from "@/modules/inspection/hooks/fields/delete/mutation.hook";
import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";

jest.mock("@/shared/lib/requests", () => ({
	createDeleteRequest: jest.fn(),
}));

jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

const mockedCreateDeleteRequest = createDeleteRequest as jest.MockedFunction<typeof createDeleteRequest>;

const mockSuccess: IMessageGlobalReturn = { message: "Vínculo de formulário excluído com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Erro ao excluir vínculo de formulário" };

describe("useDeleteFieldMutation", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve excluir o vínculo de formulário com sucesso", async () => {
		mockedCreateDeleteRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useDeleteFieldMutation(), { wrapper });
		await expect(result.current.deleteField("1")).resolves.toEqual(mockSuccess);
	});

	it("deve lançar erro ao falhar", async () => {
		mockedCreateDeleteRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useDeleteFieldMutation(), { wrapper });
		await expect(result.current.deleteField("1")).rejects.toThrow("Erro ao excluir vínculo de formulário");
	});
});
