"use client";
import useFindAllFields from "@/modules/inspection/hooks/fields/list/find-all.hook";
import { EmptyStateTable } from "@/shared/components/custom/empty/table-empty";
import { Pagination } from "@/shared/components/custom/pagination/pagination";
import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { ComponentIcon } from "lucide-react";
import React from "react";
import { usePagination } from "../../../../../../../shared/hooks/utils";
import { ISearchTerm } from "../../../../../types/tabs/search-term.type";
import { columnsFields } from "./columns";

export function FieldsTable({ searchTerm }: ISearchTerm) {
	const [rowSelection, setRowSelection] = React.useState({});
	const { currentPage, setCurrentPage, pageSize, setItemsPerPage } = usePagination();

	const { data, pagination, isLoading, hasError, error } = useFindAllFields({
		page: currentPage,
		limit: pageSize,
		search: searchTerm || "",
	});

	const table = useReactTable({
		data: data,
		columns: columnsFields,
		state: { rowSelection },
		onRowSelectionChange: setRowSelection,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const selectedCount = table.getFilteredSelectedRowModel().rows.length;

	return (
		<div className="space-y-4">
			<div className="bg-background rounded-controls overflow-x-auto border">
				<Table className="table-fixed">
					<TableHeader className="bg-primary sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => {
									const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
									const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
									return (
										<TableHead
											key={header.id}
											colSpan={header.colSpan}
											style={style}
											className="font-semibold whitespace-nowrap text-white"
										>
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									);
								})}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{hasError ? (
							<TableRow>
								<TableCell colSpan={columnsFields.length} className="h-24 text-center text-red-500">
									Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
								</TableCell>
							</TableRow>
						) : isLoading ? (
							<TableRow>
								<TableCell colSpan={columnsFields.length} className="h-24 text-center">
									<Skeleton className="h-6 w-48" />
								</TableCell>
							</TableRow>
						) : table.getRowModel().rows.length ? (
							table.getRowModel().rows.map(row => (
								<TableRow key={row.id} data-state={row.getIsSelected() && "selected"} className={`transition-colors`}>
									{row.getVisibleCells().map(cell => (
										<TableCell
											key={cell.id}
											className="max-w-[260px] overflow-hidden text-ellipsis whitespace-nowrap"
											title={String(cell.getValue() ?? "")}
										>
											{flexRender(cell.column.columnDef.cell, cell.getContext())}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell colSpan={columnsFields.length} className="h-24 text-center">
									<EmptyStateTable
										searchTerm={searchTerm}
										icon={<ComponentIcon />}
										title="Nenhum componente encontrado"
										description={
											searchTerm ? "Nenhum componente corresponde ao termo pesquisado." : "Ainda não há componentes cadastrados."
										}
										tip="Você pode tentar pesquisar por outros termos ou cadastrar um novo componente."
									/>
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					selectedCount={selectedCount}
					onPageChange={setCurrentPage}
					onPageSizeChange={size => {
						setItemsPerPage(size);
						setCurrentPage(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
}
