import { render, screen, fireEvent } from "@testing-library/react";
import { AxiosError } from "axios";
import { TableError, useTableError } from "../table-error";
import { Table, TableBody } from "@/shared/components/shadcn/table";

// Mock do shadcn components
jest.mock("@/shared/components/shadcn/table", () => ({
  Table: ({ children }: { children: React.ReactNode }) => <table>{children}</table>,
  TableBody: ({ children }: { children: React.ReactNode }) => <tbody>{children}</tbody>,
}));

jest.mock("@/shared/components/shadcn/button", () => ({
  Button: ({ children, onClick, ...props }: { children: React.ReactNode; onClick?: () => void; [key: string]: unknown }) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}));

// Wrapper para testes que precisam de estrutura de tabela
const TableWrapper = ({ children }: { children: React.ReactNode }) => (
  <Table>
    <TableBody>{children}</TableBody>
  </Table>
);

describe("TableError", () => {
  describe("Renderização básica", () => {
    it("deve renderizar com tipo not-found", () => {
      render(
        <TableWrapper>
          <TableError type="not-found" colSpan={3} />
        </TableWrapper>
      );

      expect(screen.getByText("Nenhum Resultado")).toBeInTheDocument();
      expect(screen.getByText("Nenhum dado foi encontrado para os filtros aplicados.")).toBeInTheDocument();
    });

    it("deve renderizar com tipo server-error", () => {
      render(
        <TableWrapper>
          <TableError type="server-error" colSpan={2} />
        </TableWrapper>
      );

      expect(screen.getByText("Erro do Servidor")).toBeInTheDocument();
      expect(screen.getByText("Ocorreu um erro interno no servidor. Tente novamente em alguns instantes.")).toBeInTheDocument();
    });

    it("deve renderizar com tipo network", () => {
      render(
        <TableWrapper>
          <TableError type="network" colSpan={4} />
        </TableWrapper>
      );

      expect(screen.getByText("Erro de Conexão")).toBeInTheDocument();
      expect(screen.getByText("Não foi possível conectar ao servidor. Verifique sua conexão com a internet.")).toBeInTheDocument();
    });
  });

  describe("Mensagens customizadas", () => {
    it("deve usar mensagem customizada quando fornecida", () => {
      const customMessage = "Mensagem de erro personalizada";
      
      render(
        <TableWrapper>
          <TableError type="generic" message={customMessage} colSpan={2} />
        </TableWrapper>
      );

      expect(screen.getByText(customMessage)).toBeInTheDocument();
    });

    it("deve exibir descrição adicional quando fornecida", () => {
      const description = "Descrição adicional do erro";
      
      render(
        <TableWrapper>
          <TableError type="generic" description={description} colSpan={2} />
        </TableWrapper>
      );

      expect(screen.getByText(description)).toBeInTheDocument();
    });
  });

  describe("Modo compacto", () => {
    it("deve renderizar em modo compacto", () => {
      render(
        <TableWrapper>
          <TableError type="not-found" compact={true} colSpan={3} />
        </TableWrapper>
      );

      // No modo compacto, não deve ter o título grande
      expect(screen.queryByText("Nenhum Resultado")).not.toBeInTheDocument();
      // Mas deve ter a mensagem
      expect(screen.getByText("Nenhum dado foi encontrado para os filtros aplicados.")).toBeInTheDocument();
    });
  });

  describe("Botões de ação", () => {
    it("deve renderizar botão de retry quando onRetry for fornecido", () => {
      const onRetry = jest.fn();
      
      render(
        <TableWrapper>
          <TableError type="generic" onRetry={onRetry} colSpan={2} />
        </TableWrapper>
      );

      const retryButton = screen.getByText("Tentar novamente");
      expect(retryButton).toBeInTheDocument();
      
      fireEvent.click(retryButton);
      expect(onRetry).toHaveBeenCalledTimes(1);
    });

    it("deve renderizar botão de busca quando onSearch for fornecido", () => {
      const onSearch = jest.fn();
      
      render(
        <TableWrapper>
          <TableError type="not-found" onSearch={onSearch} colSpan={2} />
        </TableWrapper>
      );

      const searchButton = screen.getByText("Buscar novamente");
      expect(searchButton).toBeInTheDocument();
      
      fireEvent.click(searchButton);
      expect(onSearch).toHaveBeenCalledTimes(1);
    });

    it("deve usar labels customizados para os botões", () => {
      const onRetry = jest.fn();
      const onSearch = jest.fn();
      
      render(
        <TableWrapper>
          <TableError
            type="generic"
            onRetry={onRetry}
            onSearch={onSearch}
            retryLabel="Recarregar Dados"
            searchLabel="Filtrar Resultados"
            colSpan={2}
          />
        </TableWrapper>
      );

      expect(screen.getByText("Recarregar Dados")).toBeInTheDocument();
      expect(screen.getByText("Filtrar Resultados")).toBeInTheDocument();
    });
  });

  describe("Conteúdo customizado", () => {
    it("deve renderizar children quando fornecido", () => {
      const customContent = <div>Conteúdo personalizado</div>;
      
      render(
        <TableWrapper>
          <TableError colSpan={2}>
            {customContent}
          </TableError>
        </TableWrapper>
      );

      expect(screen.getByText("Conteúdo personalizado")).toBeInTheDocument();
      // Não deve renderizar o conteúdo padrão
      expect(screen.queryByText("Erro Inesperado")).not.toBeInTheDocument();
    });
  });

  describe("Detalhes técnicos", () => {
    it("deve mostrar detalhes técnicos quando showDetails for true", () => {
      const error = new Error("Erro de teste");
      
      render(
        <TableWrapper>
          <TableError error={error} showDetails={true} colSpan={2} />
        </TableWrapper>
      );

      expect(screen.getByText("Detalhes técnicos")).toBeInTheDocument();
    });

    it("não deve mostrar detalhes técnicos quando showDetails for false", () => {
      const error = new Error("Erro de teste");
      
      render(
        <TableWrapper>
          <TableError error={error} showDetails={false} colSpan={2} />
        </TableWrapper>
      );

      expect(screen.queryByText("Detalhes técnicos")).not.toBeInTheDocument();
    });
  });

  describe("Atributos de acessibilidade", () => {
    it("deve ter atributos de acessibilidade corretos", () => {
      render(
        <TableWrapper>
          <TableError type="generic" colSpan={2} />
        </TableWrapper>
      );

      const alertElement = screen.getByRole("alert");
      expect(alertElement).toHaveAttribute("aria-live", "polite");
    });
  });
});

describe("useTableError hook", () => {
  const TestComponent = ({ error, isLoading }: { error: Error | null; isLoading: boolean }) => {
    const { getTableErrorProps } = useTableError();
    const errorProps = getTableErrorProps(error, isLoading);
    
    return (
      <div>
        {errorProps ? (
          <div data-testid="error-props">
            Tipo: {errorProps.type}, Erro: {errorProps.error?.message}
          </div>
        ) : (
          <div data-testid="no-error">Sem erro</div>
        )}
      </div>
    );
  };

  it("deve retornar null quando não há erro", () => {
    render(<TestComponent error={null} isLoading={false} />);
    expect(screen.getByTestId("no-error")).toBeInTheDocument();
  });

  it("deve retornar null quando está carregando", () => {
    const error = new Error("Teste");
    render(<TestComponent error={error} isLoading={true} />);
    expect(screen.getByTestId("no-error")).toBeInTheDocument();
  });

  it("deve retornar props corretas para AxiosError 404", () => {
    const error = new AxiosError("Not Found");
    Object.assign(error, { response: { status: 404 } });
    
    render(<TestComponent error={error} isLoading={false} />);
    
    const errorProps = screen.getByTestId("error-props");
    expect(errorProps).toHaveTextContent("Tipo: not-found");
  });

  it("deve retornar props corretas para AxiosError 500", () => {
    const error = new AxiosError("Internal Server Error");
    Object.assign(error, { response: { status: 500 } });
    
    render(<TestComponent error={error} isLoading={false} />);
    
    const errorProps = screen.getByTestId("error-props");
    expect(errorProps).toHaveTextContent("Tipo: server-error");
  });

  it("deve retornar props corretas para erro de network", () => {
    const error = new AxiosError("Network Error");
    error.response = undefined;
    
    render(<TestComponent error={error} isLoading={false} />);
    
    const errorProps = screen.getByTestId("error-props");
    expect(errorProps).toHaveTextContent("Tipo: network");
  });

  it("deve retornar tipo generic para erro desconhecido", () => {
    const error = new Error("Erro desconhecido");
    
    render(<TestComponent error={error} isLoading={false} />);
    
    const errorProps = screen.getByTestId("error-props");
    expect(errorProps).toHaveTextContent("Tipo: generic");
  });
});