"use client";

import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useCallback } from "react";
import { resetErrorStateAtom } from "../atoms/handlers/error.atom";
import { addMessageAtom } from "../atoms/session/add-message.atom";
import { sessionId<PERSON>tom } from "../atoms/session/info.atom";
import { updateMessageAtom } from "../atoms/session/update-message.atom";
import { currentStreamingMessageIdAtom, isStreaming<PERSON>tom, resetStreamingAtom, startStreamingAtom, stopStreamingAtom } from "../atoms/streaming/state.atom";
import { useErrorRecovery } from "./error-recovery.hook";
import { sendStreamingMessage } from "../lib/streaming-send-message.lib";
import { chatStreamService } from "../services/streaming/streaming.service";

export const useStreamingManager = () => {
	const currentSessionId = useAtomValue(sessionIdAtom);
	const addMessage = useSet<PERSON>tom(addMessage<PERSON>tom);
	const updateMessage = useSet<PERSON>tom(updateMessageAtom);
	const resetErrorState = useSetAtom(resetErrorStateAtom);
	const isStreaming = useAtomValue(isStreamingAtom);
	const currentMessageId = useAtomValue(currentStreamingMessageIdAtom);
	const startStreaming = useSetAtom(startStreamingAtom);
	const stopStreaming = useSetAtom(stopStreamingAtom);
	const resetStreaming = useSetAtom(resetStreamingAtom);

	// Error recovery hook
	const { handleError, retryOperation, resetError } = useErrorRecovery({
		onRecoverySuccess: () => {
			console.log("Chat error recovery successful");
		},
		onRecoveryFailure: error => {
			console.error("Chat error recovery failed:", error);
		},
	});

	const sendMessage = useCallback(
		async (content: string) => {
			if (!content.trim()) return;
			const sessionId = currentSessionId;
			if (!sessionId) {
				await handleError(new Error("O id da sessão não está disponível"), {
					sessionId: undefined,
					endpoint: "chat/send",
				});
				return;
			}
			if (isStreaming) stopStreaming();

			// Clear any previous errors
			resetError();

			const executeStreamingOperation = async () => {
				await sendStreamingMessage({
					sessionId,
					content,
					addMessage,
					onUpdate: updateMessage,
					setError: async (error: string | null) => {
						if (error) {
							await handleError(new Error(error), {
								sessionId,
								endpoint: "chat/send",
							});
						}
					},
					onStreamingStart: messageId => {
						startStreaming(messageId);
					},
				});
			};

			try {
				await retryOperation(executeStreamingOperation);
				stopStreaming();
			} catch (error) {
				stopStreaming();
				// Error is already handled by the error recovery system
				console.debug("Streaming operation failed after all retries:", error);
			}
		},
		[addMessage, currentSessionId, updateMessage, isStreaming, stopStreaming, startStreaming, handleError, retryOperation, resetError],
	);

	const stop = useCallback(() => {
		if (isStreaming) {
			chatStreamService.abortStream();
			stopStreaming();
			if (currentMessageId) {
				updateMessage(currentMessageId, {
					content: "Resposta interrompida pelo usuário.",
					isStreaming: false,
					isError: false,
				});
			}
		}
	}, [isStreaming, stopStreaming, currentMessageId, updateMessage]);

	const reset = useCallback(() => {
		resetStreaming();
		resetError();
	}, [resetStreaming, resetError]);

	return {
		sendMessage,
		stop,
		reset,
		isStreaming,
		// isPaused,
		currentMessageId,
		canSend: !isStreaming && !!currentSessionId,
		canStop: isStreaming,
		canResume: isStreaming,
	};
};
