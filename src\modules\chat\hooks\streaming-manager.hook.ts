"use client";

import { useAtomValue, useSetAtom } from "jotai";
import { useCallback } from "react";
import { chatErrorAtom } from "../atoms/handlers/error.atom";
import { addMessageAtom } from "../atoms/session/add-message.atom";
import { sessionId<PERSON>tom } from "../atoms/session/info.atom";
import { updateMessageAtom } from "../atoms/session/update-message.atom";
import { currentStreamingMessageIdAtom, isStreamingAtom, resetStreamingAtom, startStreamingAtom, stopStreamingAtom } from "../atoms/streaming/state.atom";
import { sendStreamingMessage } from "../lib/streaming-send-message.lib";
import { chatStreamService } from "../services/streaming/streaming.service";

export const useStreamingManager = () => {
	const currentSessionId = useAtomValue(sessionIdAtom);
	const addMessage = useSetAtom(addMessageAtom);
	const updateMessage = useSetAtom(updateMessage<PERSON>tom);
	const setError = useSetAtom(chatErrorAtom);
	const isStreaming = useAtomValue(isStreamingAtom);
	// const isPaused = useAtomValue(isPausedAtom);
	const currentMessageId = useAtomValue(currentStreamingMessageIdAtom);
	const startStreaming = useSetAtom(startStreamingAtom);
	const stopStreaming = useSetAtom(stopStreamingAtom);
	const resetStreaming = useSetAtom(resetStreamingAtom);

	const sendMessage = useCallback(
		async (content: string) => {
			if (!content.trim()) return;
			const sessionId = currentSessionId;
			if (!sessionId) throw new Error("O id da sessão não está disponível");
			if (isStreaming) stopStreaming();

			try {
				await sendStreamingMessage({
					sessionId,
					content,
					addMessage,
					onUpdate: updateMessage,
					setError,
					onStreamingStart: messageId => {
						startStreaming(messageId);
					},
				});
				stopStreaming();
			} catch (error) {
				stopStreaming();
				setError(error instanceof Error ? error.message : "Erro ao enviar mensagem");
			}
		},
		[addMessage, currentSessionId, setError, updateMessage, isStreaming, stopStreaming, startStreaming],
	);

	const stop = useCallback(() => {
		if (isStreaming) {
			chatStreamService.abortStream();
			stopStreaming();
			if (currentMessageId) {
				updateMessage(currentMessageId, {
					content: "Resposta interrompida pelo usuário.",
					isStreaming: false,
					isError: false,
				});
			}
		}
	}, [isStreaming, stopStreaming, currentMessageId, updateMessage]);

	const reset = useCallback(() => {
		resetStreaming();
		setError(null);
	}, [resetStreaming, setError]);

	return {
		sendMessage,
		stop,
		reset,
		isStreaming,
		// isPaused,
		currentMessageId,
		canSend: !isStreaming && !!currentSessionId,
		canStop: isStreaming,
		canResume: isStreaming,
	};
};
