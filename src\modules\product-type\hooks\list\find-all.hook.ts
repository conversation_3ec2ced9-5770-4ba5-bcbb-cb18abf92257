import { IHookPaginatedProps } from "@/shared/types/pagination/types";
import { useQuery } from "@tanstack/react-query";
import { productTypeKeys } from "../../constants/query";

export const useFindAllProductTypes = (params: IHookPaginatedProps) => {
	const { data, isLoading, isFetched } = useQuery({
		queryKey: productTypeKeys.list({
			...params,
		}),
		// queryFn: () => createGetRequest<IResponsePaginated<IProductTypeDto>>(PRODUCT_TYPE_ENDPOINTS.FIND_ALL(params)),
	});

	return { data, isLoading, isFetched };
};
