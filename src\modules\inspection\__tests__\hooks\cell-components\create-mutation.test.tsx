import { useCreateCellByComponentMutation } from "@/modules/inspection/hooks/cell-components/create/mutation.hook";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook } from "@testing-library/react";

jest.mock("@/shared/lib/requests", () => ({
	createPostRequest: jest.fn(),
}));

jest.mock("@/core/toast", () => ({
	toast: { promise: jest.fn(p => p) },
}));

const mockedCreatePostRequest = createPostRequest as jest.MockedFunction<typeof createPostRequest>;

const mockSuccess: IMessageGlobalReturn = { message: "Célula por componente criada com sucesso" };
const mockError: IMessageGlobalReturn = { message: "Erro ao criar célula por componente" };

describe("useCreateCellByComponentMutation", () => {
	let queryClient: QueryClient;
	beforeEach(() => {
		queryClient = new QueryClient();
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve criar célula por componente com sucesso", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: true, data: mockSuccess, status: 200 });
		const { result } = renderHook(() => useCreateCellByComponentMutation(), { wrapper });
		await expect(result.current.createCellComponent({ cellProductionId: 2, componentId: 0 })).resolves.toEqual(mockSuccess);
	});

	it("deve lançar erro ao falhar", async () => {
		mockedCreatePostRequest.mockResolvedValueOnce({ success: false, data: mockError, status: 400 });
		const { result } = renderHook(() => useCreateCellByComponentMutation(), { wrapper });
		await expect(result.current.createCellComponent({ cellProductionId: 2, componentId: 0 })).rejects.toThrow("Erro ao criar célula por componente");
	});
});
