"use client";

import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { TableError, useTableError } from "./table-error";
import { useState } from "react";
import { Button } from "@/shared/components/shadcn/button";

/**
 * Exemplo de uso do componente TableError
 */
export const TableErrorExample = () => {
  const [errorType, setErrorType] = useState<string>("not-found");
  const [hasError, setHasError] = useState(true);
  const { getTableErrorProps } = useTableError();

  // Simula diferentes tipos de erro
  const simulateError = (type: string) => {
    setErrorType(type);
    setHasError(true);
  };

  // Simula erro de rede
  const networkError = Object.assign(new Error("Network Error"), { response: null });

  // Simula erro 404
  const notFoundError = Object.assign(new Error("Not Found"), { response: { status: 404 } });

  // Simula erro 500
  const serverError = Object.assign(new Error("Internal Server Error"), { response: { status: 500 } });

  const mockErrors: Record<string, Error> = {
    "network": networkError,
    "not-found": notFoundError,
    "server-error": serverError,
    "permission-denied": new Error("Permission Denied"),
    "timeout": new Error("Request Timeout"),
    "validation": new Error("Validation Error"),
    "generic": new Error("Generic Error")
  };

  const currentError = hasError ? mockErrors[errorType] : null;
  const errorProps = getTableErrorProps(currentError);

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Exemplos de TableError</h2>
        
        {/* Controles para testar diferentes tipos de erro */}
        <div className="flex flex-wrap gap-2 p-4 bg-gray-50 rounded-lg">
          <Button
            onClick={() => setHasError(false)}
            variant={!hasError ? "default" : "outline"}
            size="sm"
          >
            Sem Erro
          </Button>
          {Object.keys(mockErrors).map((type) => (
            <Button
              key={type}
              onClick={() => simulateError(type)}
              variant={hasError && errorType === type ? "default" : "outline"}
              size="sm"
            >
              {type.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}
            </Button>
          ))}
        </div>
      </div>

      {/* Exemplo 1: TableError básico */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">1. Erro Básico</h3>
        <div className="rounded-lg border overflow-hidden">
          <Table>
            <TableHeader className="bg-[#004475]">
              <TableRow className="bg-[#004475] hover:bg-[#004475]/80 border-0">
                <TableHead className="text-white font-semibold">ID</TableHead>
                <TableHead className="text-white font-semibold">Nome</TableHead>
                <TableHead className="text-white font-semibold">Email</TableHead>
                <TableHead className="text-white font-semibold">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {hasError ? (
                <TableError
                  type={errorType as "network" | "not-found" | "server-error" | "permission-denied" | "timeout" | "validation" | "generic"}
                  colSpan={4}
                  onRetry={() => setHasError(false)}
                  onSearch={() => console.log("Buscar novamente")}
                  showDetails={true}
                  {...errorProps}
                />
              ) : (
                <TableRow>
                  <td className="p-4" colSpan={4}>
                    <div className="text-center text-green-600 font-medium">
                      ✅ Dados carregados com sucesso!
                    </div>
                  </td>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Exemplo 2: TableError compacto */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">2. Erro Compacto</h3>
        <div className="rounded-lg border overflow-hidden">
          <Table>
            <TableHeader className="bg-[#004475]">
              <TableRow className="bg-[#004475] hover:bg-[#004475]/80 border-0">
                <TableHead className="text-white font-semibold">Produto</TableHead>
                <TableHead className="text-white font-semibold">Quantidade</TableHead>
                <TableHead className="text-white font-semibold">Preço</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {hasError ? (
                <TableError
                  type={errorType as "network" | "not-found" | "server-error" | "permission-denied" | "timeout" | "validation" | "generic"}
                  colSpan={3}
                  compact={true}
                  onRetry={() => setHasError(false)}
                />
              ) : (
                <TableRow>
                  <td className="p-4" colSpan={3}>
                    <div className="text-center text-green-600 font-medium">
                      ✅ Produtos carregados!
                    </div>
                  </td>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Exemplo 3: TableError customizado */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">3. Erro Customizado</h3>
        <div className="rounded-lg border overflow-hidden">
          <Table>
            <TableHeader className="bg-[#004475]">
              <TableRow className="bg-[#004475] hover:bg-[#004475]/80 border-0">
                <TableHead className="text-white font-semibold">Data</TableHead>
                <TableHead className="text-white font-semibold">Evento</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {hasError ? (
                <TableError
                  message="Não foi possível carregar o histórico de eventos"
                  description="Verifique se você tem permissão para visualizar este conteúdo"
                  colSpan={2}
                  onRetry={() => setHasError(false)}
                  retryLabel="Recarregar Histórico"
                  searchLabel="Filtrar Eventos"
                />
              ) : (
                <TableRow>
                  <td className="p-4" colSpan={2}>
                    <div className="text-center text-green-600 font-medium">
                      ✅ Histórico carregado!
                    </div>
                  </td>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Exemplo 4: TableError com conteúdo customizado */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">4. Conteúdo Totalmente Customizado</h3>
        <div className="rounded-lg border overflow-hidden">
          <Table>
            <TableHeader className="bg-[#004475]">
              <TableRow className="bg-[#004475] hover:bg-[#004475]/80 border-0">
                <TableHead className="text-white font-semibold">Relatório</TableHead>
                <TableHead className="text-white font-semibold">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {hasError ? (
                <TableError colSpan={2}>
                  <div className="text-center space-y-4">
                    <div className="text-6xl">🚧</div>
                    <h3 className="text-xl font-bold text-gray-700">
                      Funcionalidade em Desenvolvimento
                    </h3>
                    <p className="text-gray-500 max-w-md">
                      Esta seção ainda está sendo desenvolvida. 
                      Em breve você poderá visualizar os relatórios aqui.
                    </p>
                    <Button 
                      onClick={() => setHasError(false)}
                      className="mt-4"
                    >
                      Entendi
                    </Button>
                  </div>
                </TableError>
              ) : (
                <TableRow>
                  <td className="p-4" colSpan={2}>
                    <div className="text-center text-green-600 font-medium">
                      ✅ Relatórios disponíveis!
                    </div>
                  </td>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Código de exemplo */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Como Usar</h3>
        <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono overflow-x-auto">
          <pre>{`// Uso básico
<TableError 
  type="not-found"
  colSpan={4}
  onRetry={() => refetch()}
/>

// Com hook para React Query
const { getTableErrorProps } = useTableError();
const errorProps = getTableErrorProps(error, isLoading);

{error && (
  <TableError 
    colSpan={columns.length}
    onRetry={() => refetch()}
    {...errorProps}
  />
)}

// Customizado
<TableError 
  message="Mensagem personalizada"
  description="Descrição adicional"
  onRetry={() => handleRetry()}
  onSearch={() => handleSearch()}
  showDetails={true}
/>`}</pre>
        </div>
      </div>
    </div>
  );
};