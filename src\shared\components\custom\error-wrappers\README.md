# TableError Component

Componente wrapper extremamente intuitivo para exibir erros em tabelas, seguindo o design system do projeto.

## 🎯 Características

- **Detecção automática** de tipos de erro baseada no error object
- **7 tipos diferentes** de erro com cores e ícones específicos
- **Modo compacto** para tabelas com pouco espaço
- **Totalmente customizável** com children props
- **Integração fácil** com React Query e outras bibliotecas de fetch
- **Acessibilidade** completa (ARIA, screen readers)
- **Design responsivo** e consistente com o projeto

## 📦 Tipos de Erro Suportados

| Tipo | Ícone | Cor | Quando Usar |
|------|-------|-----|-------------|
| `network` | 📶 | Laranja | Problemas de conexão, offline |
| `not-found` | 🔍 | Azul | Nenhum resultado encontrado |
| `server-error` | 🗄️ | Vermelho | Erros 5xx do servidor |
| `permission-denied` | ❌ | Roxo | Erros 401/403 de autorização |
| `timeout` | ⚠️ | Amarelo | Timeout de requisições |
| `validation` | ⚠️ | Âmbar | Erros de validação |
| `generic` | ⚠️ | Cinza | Erro genérico/desconhecido |

## 🚀 Uso Básico

```tsx
import { TableError } from "@/shared/components/custom/error-wrappers/table-error";

// Erro simples
<TableError 
  type="not-found"
  colSpan={4}
  onRetry={() => refetch()}
/>

// Com detecção automática do tipo
<TableError 
  error={error}
  colSpan={columns.length}
  onRetry={() => refetch()}
  showDetails={isDev}
/>
```

## 🔧 Integração com React Query

```tsx
import { useTableError } from "@/shared/components/custom/error-wrappers/table-error";

const { data, error, isLoading, refetch } = useQuery(...);
const { getTableErrorProps } = useTableError();

return (
  <Table>
    <TableHeader>...</TableHeader>
    <TableBody>
      {error ? (
        <TableError
          colSpan={columns.length}
          onRetry={() => refetch()}
          {...getTableErrorProps(error, isLoading)}
        />
      ) : (
        // Renderizar dados normalmente
        data?.map(item => <TableRow key={item.id}>...</TableRow>)
      )}
    </TableBody>
  </Table>
);
```

## 📋 Props Completas

### ITableErrorProps

| Prop | Tipo | Padrão | Descrição |
|------|------|--------|-----------|
| `type` | `TTableErrorType` | `undefined` | Tipo do erro (detectado automaticamente se não fornecido) |
| `message` | `string` | `undefined` | Mensagem customizada (sobrescreve a padrão) |
| `description` | `string` | `undefined` | Descrição adicional do erro |
| `onRetry` | `() => void` | `undefined` | Função para tentar novamente |
| `onSearch` | `() => void` | `undefined` | Função de busca/filtro alternativa |
| `retryLabel` | `string` | `"Tentar novamente"` | Texto do botão de retry |
| `searchLabel` | `string` | `"Buscar novamente"` | Texto do botão de busca |
| `compact` | `boolean` | `false` | Modo compacto (apenas ícone + mensagem) |
| `className` | `string` | `undefined` | Classes CSS adicionais |
| `children` | `ReactNode` | `undefined` | Conteúdo customizado |
| `colSpan` | `number` | `1` | Número de colunas para colspan |
| `minHeight` | `string` | `"200px"` | Altura mínima do container |
| `error` | `Error \| AxiosError` | `undefined` | Object de erro para detalhes técnicos |
| `showDetails` | `boolean` | `false` | Mostrar detalhes técnicos (recomendado apenas em dev) |

## 🎨 Exemplos de Uso

### 1. Erro Básico com Retry

```tsx
<TableError
  type="server-error"
  colSpan={5}
  onRetry={() => {
    console.log("Tentando novamente...");
    refetch();
  }}
  onSearch={() => {
    console.log("Abrindo filtros...");
    openFilters();
  }}
/>
```

### 2. Modo Compacto

```tsx
<TableError
  type="not-found"
  message="Nenhum produto encontrado"
  colSpan={3}
  compact={true}
  onRetry={() => refetch()}
/>
```

### 3. Conteúdo Totalmente Customizado

```tsx
<TableError colSpan={4}>
  <div className="text-center space-y-4">
    <div className="text-6xl">🚧</div>
    <h3 className="text-xl font-bold">Em Manutenção</h3>
    <p className="text-gray-500">
      Este módulo está temporariamente indisponível.
    </p>
    <Button onClick={() => window.location.reload()}>
      Recarregar Página
    </Button>
  </div>
</TableError>
```

### 4. Com Mensagem Personalizada

```tsx
<TableError
  message="Não foi possível carregar os dados do relatório"
  description="Verifique sua conexão e tente novamente"
  colSpan={6}
  onRetry={() => refetch()}
  retryLabel="Recarregar Relatório"
  showDetails={process.env.NODE_ENV === "development"}
  error={error}
/>
```

## 🔍 Hook useTableError

O hook `useTableError` facilita a integração com bibliotecas de fetch:

```tsx
const { getTableErrorProps } = useTableError();

// Retorna props apropriadas baseadas no erro
const errorProps = getTableErrorProps(error, isLoading);

// errorProps será null se não houver erro ou estiver carregando
// Caso contrário, retorna: { type, error, showDetails }
```

## 🎯 Boas Práticas

### ✅ Faça

- Use `colSpan={columns.length}` para ocupar toda a largura da tabela
- Sempre forneça uma função `onRetry` quando possível
- Use `showDetails={isDevelopment}` para mostrar detalhes técnicos apenas em desenvolvimento
- Configure mensagens específicas para diferentes contextos de negócio
- Use o modo `compact` em tabelas pequenas ou laterais

### ❌ Evite

- Usar o componente fora de `<TableBody>`
- Esquecer de definir `colSpan` adequadamente
- Mostrar detalhes técnicos em produção
- Usar mensagens genéricas demais em contextos específicos

## 🔧 Customização

### Cores e Estilos

O componente usa as classes Tailwind do projeto e segue o design system. Para customizar:

```tsx
<TableError
  className="custom-error-styles"
  // Ou use children para controle total
>
  <CustomErrorContent />
</TableError>
```

### Detecção Automática de Tipos

A função `getErrorType` analisa o error object e determina o tipo automaticamente:

- **AxiosError com status 404** → `not-found`
- **AxiosError com status 401/403** → `permission-denied`  
- **AxiosError com status 5xx** → `server-error`
- **AxiosError sem response** → `network`
- **Error com "network" na mensagem** → `network`
- **Error com "timeout" na mensagem** → `timeout`
- **Qualquer outro** → `generic`

## 📱 Responsividade

O componente é totalmente responsivo:

- **Desktop**: Layout completo com ícones grandes e botões espaçados
- **Tablet**: Layout adaptado com elementos menores
- **Mobile**: Modo compacto automático em telas pequenas

## ♿ Acessibilidade

- `role="alert"` para anunciar erros aos leitores de tela
- `aria-live="polite"` para atualizações não-intrusivas
- Labels apropriados nos botões
- Contraste adequado em todas as variações de cor
- Navegação por teclado funcional

## 🔗 Arquivos Relacionados

- `table-error.tsx` - Componente principal
- `table-error.example.tsx` - Exemplos de uso
- `README.md` - Esta documentação