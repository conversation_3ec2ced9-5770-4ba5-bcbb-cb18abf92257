/**
 * Exemplo prático de integração do TableError com uma tabela real
 * Este arquivo demonstra como usar o componente em cenários reais do projeto
 */

"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { TableError, useTableError } from "./table-error";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Button } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { Search, Filter } from "lucide-react";

// Simula uma API de dados
const fetchTableData = async (filter: string = ""): Promise<Array<{ id: number; name: string; email: string; status: string }>> => {
  // Simula diferentes cenários de erro baseado no filtro
  await new Promise(resolve => setTimeout(resolve, 1000)); // Simula delay

  if (filter === "error-network") {
    const error = Object.assign(new Error("Failed to fetch"), { response: null });
    throw error;
  }

  if (filter === "error-404") {
    const error = Object.assign(new Error("Not Found"), { response: { status: 404 } });
    throw error;
  }

  if (filter === "error-500") {
    const error = Object.assign(new Error("Internal Server Error"), { response: { status: 500 } });
    throw error;
  }

  if (filter === "error-permission") {
    const error = Object.assign(new Error("Permission Denied"), { response: { status: 403 } });
    throw error;
  }

  if (filter === "empty") {
    return [];
  }

  // Retorna dados de exemplo
  return [
    { id: 1, name: "João Silva", email: "<EMAIL>", status: "Ativo" },
    { id: 2, name: "Maria Santos", email: "<EMAIL>", status: "Inativo" },
    { id: 3, name: "Pedro Costa", email: "<EMAIL>", status: "Ativo" },
  ].filter(item => 
    !filter || 
    item.name.toLowerCase().includes(filter.toLowerCase()) ||
    item.email.toLowerCase().includes(filter.toLowerCase())
  );
};

/**
 * Componente de tabela com integração completa do TableError
 */
export const UserTableWithErrorHandling = () => {
  const [filter, setFilter] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const { getTableErrorProps } = useTableError();

  // Query com React Query
  const { 
    data, 
    error, 
    isLoading, 
    refetch,
    isRefetching 
  } = useQuery({
    queryKey: ["users", filter],
    queryFn: () => fetchTableData(filter),
    retry: false, // Desabilitamos retry para demonstrar o erro
  });

  const columns = [
    { key: "id", label: "ID" },
    { key: "name", label: "Nome" },
    { key: "email", label: "Email" },
    { key: "status", label: "Status" },
  ];

  // Props do erro usando o hook
  const errorProps = getTableErrorProps(error, isLoading);

  const handleSearch = () => {
    setFilter(searchTerm);
  };

  const handleRetry = () => {
    refetch();
  };

  const handleClearFilters = () => {
    setFilter("");
    setSearchTerm("");
  };

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-gray-800">
          Usuários do Sistema
        </h2>

        {/* Barra de pesquisa */}
        <div className="flex gap-4 items-end">
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
              Pesquisar usuários
            </label>
            <Input
              id="search"
              type="text"
              placeholder="Digite o nome ou email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
            />
          </div>
          <Button onClick={handleSearch} disabled={isLoading}>
            <Search className="w-4 h-4" />
            Buscar
          </Button>
          <Button onClick={handleClearFilters} variant="outline" disabled={isLoading}>
            <Filter className="w-4 h-4" />
            Limpar
          </Button>
        </div>

        {/* Botões de teste para demonstração */}
        <div className="flex flex-wrap gap-2 p-4 bg-gray-50 rounded-lg">
          <p className="w-full text-sm text-gray-600 mb-2">
            🧪 <strong>Testes de cenários:</strong>
          </p>
          <Button size="sm" variant="outline" onClick={() => setFilter("")}>
            ✅ Dados Normais
          </Button>
          <Button size="sm" variant="outline" onClick={() => setFilter("empty")}>
            📭 Vazio
          </Button>
          <Button size="sm" variant="outline" onClick={() => setFilter("error-network")}>
            📶 Erro de Rede
          </Button>
          <Button size="sm" variant="outline" onClick={() => setFilter("error-404")}>
            🔍 Não Encontrado
          </Button>
          <Button size="sm" variant="outline" onClick={() => setFilter("error-500")}>
            🗄️ Erro do Servidor
          </Button>
          <Button size="sm" variant="outline" onClick={() => setFilter("error-permission")}>
            🔒 Sem Permissão
          </Button>
        </div>
      </div>

      {/* Status da consulta */}
      {(isLoading || isRefetching) && (
        <div className="text-center py-4 text-blue-600">
          🔄 Carregando dados...
        </div>
      )}

      {/* Tabela principal */}
      <div className="rounded-lg border overflow-hidden shadow-sm">
        <Table>
          <TableHeader className="bg-[#004475]">
            <TableRow className="bg-[#004475] hover:bg-[#004475]/80 border-0">
              {columns.map((column) => (
                <TableHead key={column.key} className="text-white font-semibold">
                  {column.label}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {error ? (
              // Erro: usar o TableError com props automáticas
              <TableError
                colSpan={columns.length}
                onRetry={handleRetry}
                onSearch={handleClearFilters}
                searchLabel="Limpar Filtros"
                showDetails={process.env.NODE_ENV === "development"}
                {...errorProps}
              />
            ) : data && data.length === 0 ? (
              // Nenhum resultado: usar TableError específico para vazio
              <TableError
                type="not-found"
                message="Nenhum usuário encontrado"
                description={filter ? "Tente ajustar os filtros de pesquisa" : "Não há usuários cadastrados no sistema"}
                colSpan={columns.length}
                onSearch={handleClearFilters}
                searchLabel="Limpar Filtros"
              />
            ) : (
              // Dados: renderizar normalmente
              data?.map((user) => (
                <TableRow key={user.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">{user.id}</TableCell>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                        user.status === "Ativo"
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {user.status}
                    </span>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Informações adicionais */}
      <div className="text-sm text-gray-500 space-y-2">
        <p>
          💡 <strong>Dica:</strong> Este exemplo demonstra como o TableError se integra perfeitamente com React Query
        </p>
        <ul className="list-disc list-inside space-y-1 ml-4">
          <li>Detecção automática do tipo de erro baseada na resposta da API</li>
          <li>Integração com estados de loading e refetch</li>
          <li>Diferenciação entre &quot;erro&quot; e &quot;sem resultados&quot;</li>
          <li>Botões de ação contextuais (retry, limpar filtros)</li>
          <li>Detalhes técnicos visíveis apenas em desenvolvimento</li>
        </ul>
      </div>
    </div>
  );
};

/**
 * Exemplo mais simples para casos básicos
 */
export const SimpleTableWithError = () => {
  const [hasError, setHasError] = useState(false);
  const [errorType, setErrorType] = useState<"network" | "server-error" | "not-found">("not-found");

  return (
    <div className="space-y-4 p-6">
      <h3 className="text-lg font-semibold">Exemplo Simples</h3>
      
      <div className="flex gap-2">
        <Button onClick={() => setHasError(false)} variant={!hasError ? "default" : "outline"} size="sm">
          ✅ OK
        </Button>
        <Button onClick={() => { setHasError(true); setErrorType("not-found"); }} size="sm" variant="outline">
          🔍 Vazio
        </Button>
        <Button onClick={() => { setHasError(true); setErrorType("network"); }} size="sm" variant="outline">
          📶 Rede
        </Button>
        <Button onClick={() => { setHasError(true); setErrorType("server-error"); }} size="sm" variant="outline">
          🗄️ Servidor
        </Button>
      </div>

      <Table>
        <TableHeader className="bg-[#004475]">
          <TableRow className="bg-[#004475] hover:bg-[#004475]/80 border-0">
            <TableHead className="text-white">Item</TableHead>
            <TableHead className="text-white">Valor</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {hasError ? (
            <TableError
              type={errorType}
              colSpan={2}
              compact={true}
              onRetry={() => setHasError(false)}
            />
          ) : (
            <TableRow>
              <TableCell>Exemplo</TableCell>
              <TableCell>Dados carregados ✅</TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};