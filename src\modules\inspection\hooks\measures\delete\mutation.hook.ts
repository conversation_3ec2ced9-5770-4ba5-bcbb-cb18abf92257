import { toast } from "@/core/toast";
import { MEASURES_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createDeleteRequest } from "@/shared/lib/requests";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { inspectionKeys } from "../../../constants/query/keys";

export function useDeleteMeasureMutation() {
	const queryClient = useQueryClient();

	const deleteMeasures = useMutation({
		mutationKey: inspectionKeys.measures.custom("delete"),
		mutationFn: async (id: string) => {
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(MEASURES_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.measures.invalidateAll(queryClient),
	});

	return {
		deleteMeasure: (id: string) =>
			toast.promise(deleteMeasures.mutateAsync(id), {
				loading: "Excluindo medida...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
}
