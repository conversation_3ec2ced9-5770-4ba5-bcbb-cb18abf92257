import { useFindAllCellByComponents } from "@/modules/inspection/hooks/cell-components/list/find-all.hook";
import { ICellByComponentDto } from "@/modules/inspection/types/cell-components/dtos/find-all.dto";

import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";

if (!globalThis.fetch) globalThis.fetch = jest.fn();
jest.mock("@/shared/lib/requests", () => ({
	createGetRequest: jest.fn(),
}));

const mockedCreateGetRequest = createGetRequest as jest.MockedFunction<typeof createGetRequest>;

const mockCellByComponentList: ICellByComponentDto[] = [
	{ id: 1, cellName: "Célula 1", componentName: "Componente A" },
	{ id: 2, cellName: "Célula 2", componentName: "Componente B" },
	{ id: 3, cellName: "Célula 3", componentName: "Componente C" },
];

const mockPaginatedData: IResponsePaginated<ICellByComponentDto> = {
	data: mockCellByComponentList,
	totalItems: 3,
	itemCount: 3,
	itemsPerPage: 10,
	currentPage: 1,
	totalPages: 1,
};

const mockSuccessResponse: ApiResponse<IResponsePaginated<ICellByComponentDto>> = {
	data: mockPaginatedData,
	success: true,
	status: 200,
};

const mockEmptyResponse: ApiResponse<IResponsePaginated<ICellByComponentDto>> = {
	success: false,
	data: { message: "Nenhuma célula por componente encontrada" },
	status: 404,
};

const mockErrorResponse: ApiResponse<IResponsePaginated<ICellByComponentDto>> = {
	success: false,
	data: { message: "Erro interno do servidor" },
	status: 500,
};

describe("useFindAllInspectionCellComponents", () => {
	let queryClient: QueryClient;

	beforeEach(() => {
		queryClient = new QueryClient({
			defaultOptions: {
				queries: {
					retry: false,
				},
			},
		});
		jest.clearAllMocks();
	});

	const wrapper = ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;

	it("deve retornar dados vazios no estado inicial", () => {
		mockedCreateGetRequest.mockImplementation(() => new Promise(() => {}));
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllCellByComponents(params), { wrapper });
		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.isLoading).toBe(true);
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(false);
	});

	it("deve retornar dados de células por componente com sucesso", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);
		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllCellByComponents(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual(mockCellByComponentList);
		expect(result.current.pagination).toEqual({
			totalItems: 3,
			itemsPerPage: 10,
			currentPage: 1,
			totalPages: 1,
		});
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(false);
		expect(result.current.error).toBe(false);
	});

	it("deve lidar com resposta vazia (404)", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockEmptyResponse);

		const params = { page: 1, limit: 10, search: "célula por componente inexistente" };
		const { result } = renderHook(() => useFindAllCellByComponents(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.hasError).toBe(false);
		expect(result.current.isEmpty).toBe(true);
		expect(result.current.error).toBe(false);
	});

	it("deve lidar com erro do servidor", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockErrorResponse);

		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllCellByComponents(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([]);
		expect(result.current.pagination).toBeNull();
		expect(result.current.hasError).toBe(true);
		expect(result.current.isEmpty).toBe(false);
		expect(result.current.error).toBe("Erro interno do servidor");
	});

	it("deve funcionar com parâmetros de paginação diferentes", async () => {
		const mockPaginatedResponse: ApiResponse<IResponsePaginated<ICellByComponentDto>> = {
			success: true,
			data: {
				...mockPaginatedData,
				currentPage: 2,
				itemsPerPage: 5,
				totalPages: 2,
			},
			status: 200,
		};

		mockedCreateGetRequest.mockResolvedValueOnce(mockPaginatedResponse);

		const params = { page: 2, limit: 5, search: "" };
		const { result } = renderHook(() => useFindAllCellByComponents(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.pagination).toEqual({
			totalItems: 3,
			itemsPerPage: 5,
			currentPage: 2,
			totalPages: 2,
		});
	});

	it("deve funcionar com parâmetro de busca", async () => {
		const mockSearchData: IResponsePaginated<ICellByComponentDto> = {
			data: [mockCellByComponentList[0]],
			totalItems: 1,
			itemCount: 1,
			itemsPerPage: 10,
			currentPage: 1,
			totalPages: 1,
		};

		const mockSearchResponse: ApiResponse<IResponsePaginated<ICellByComponentDto>> = {
			success: true,
			data: mockSearchData,
			status: 200,
		};

		mockedCreateGetRequest.mockResolvedValueOnce(mockSearchResponse);

		const params = { page: 1, limit: 10, search: "Setor A" };
		const { result } = renderHook(() => useFindAllCellByComponents(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([mockCellByComponentList[0]]);
		expect(result.current.pagination?.totalItems).toBe(1);
	});

	it("deve funcionar sem parâmetros opcionais", async () => {
		mockedCreateGetRequest.mockResolvedValueOnce(mockSuccessResponse);

		const params = {};
		const { result } = renderHook(() => useFindAllCellByComponents(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual(mockCellByComponentList);
		expect(mockedCreateGetRequest).toHaveBeenCalledWith(expect.stringContaining("/cell"));
	});

	it("deve lidar com falha na requisição (erro de rede)", async () => {
		mockedCreateGetRequest.mockRejectedValueOnce(new Error("Erro de rede"));

		const params = { page: 1, limit: 10, search: "" };
		const { result } = renderHook(() => useFindAllCellByComponents(params), { wrapper });

		await waitFor(() => {
			expect(result.current.isLoading).toBe(false);
		});

		expect(result.current.data).toEqual([]);
		expect(result.current.hasError).toBe(true);
		expect(result.current.isEmpty).toBe(false);
	});
});
