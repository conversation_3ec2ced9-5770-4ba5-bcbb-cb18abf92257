import {
	Box,
	ChartNoAxesCombinedIcon,
	HomeIcon,
	Ruler,
	RulerDimensionLine,
	ScrollText,
	SearchIcon,
	ShoppingBag,
	SlidersHorizontalIcon,
	SlidersVertical,
	UsersIcon,
} from "lucide-react";
import { PATHS_CONFIG } from "./config";
import { ICreateItemGroupPathManager } from "./types";

export const pathItems: ICreateItemGroupPathManager[] = [
	{
		title: "Dashboard",
		items: [
			{
				id: "home",
				name: "Tela Inicial",
				description: "Bem-vindo ao S.I.M.P - Sistema Integrado de Medição e Manufatura Pormade",
				requiredPermissions: [],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.DASHBOARD,
				icon: HomeIcon,
			},
		],
	},
	{
		title: "Medição",
		items: [
			{
				name: "Pré-medição",
				id: "pre-measurement",
				description: "<PERSON>ere<PERSON><PERSON> as pré-medições",
				requiredPermissions: [
					{
						action: "read",
						subject: "all",
					},
				],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.MEDICAO.subPaths.PRE_MEDICAO,
				icon: RulerDimensionLine,
			},
			{
				name: "Planilha de Medição",
				id: "measurement-sheet",
				description: "Gerencie as medições",
				requiredPermissions: [
					{
						action: "read",
						subject: "all",
					},
				],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.MEDICAO.subPaths.PLANILHA_MEDICAO,
				icon: Ruler,
			},
		],
	},
	{
		title: "Ciclo do Pedido",
		items: [
			{
				name: "Pedidos",
				id: "orders",
				description: "Gerencie os pedidos",
				requiredPermissions: [
					{
						action: "read",
						subject: "all",
					},
				],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.CICLO_PEDIDO.subPaths.PEDIDOS,
				icon: ShoppingBag,
			},
			{
				name: "Lotes",
				id: "lots",
				description: "Gerencie os lotes",
				requiredPermissions: [
					{
						action: "read",
						subject: "all",
					},
				],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.CICLO_PEDIDO.subPaths.LOTES,
				icon: Box,
			},
			{
				name: "Resumo",
				id: "summary-lot",
				description: "Resumo do ciclo do pedido",
				requiredPermissions: [
					{
						action: "read",
						subject: "all",
					},
				],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.CICLO_PEDIDO.subPaths.RESUMO,
				icon: ScrollText,
			},
		],
	},
	{
		title: "Produção",
		items: [
			{
				name: "Cadastros",
				id: "production-sectors",
				description: "Gerencie os setores de produção",
				requiredPermissions: [
					{
						action: "read",
						subject: "all",
					},
				],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS,
				icon: SlidersVertical,
				subItems: [
					{
						name: "Apontamentos",
						id: "apontamentos",
						description: "Gerencie os apontamentos de produção",
						requiredPermissions: [
							{
								action: "read",
								subject: "all",
							},
						],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.APONTAMENTOS,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Atividades",
						id: "activity",
						description: "Gerencie as atividades de produção",
						requiredPermissions: [
							{
								action: "read",
								subject: "all",
							},
						],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.ATIVIDADE,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Células",
						id: "cells",
						description: "Gerencie as células de produção",
						requiredPermissions: [
							{
								action: "read",
								subject: "all",
							},
						],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.CELULAS,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Estoque",
						id: "inventory",
						description: "Gerencie o controle de estoque",
						requiredPermissions: [
							{
								action: "read",
								subject: "all",
							},
						],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.ESTOQUE,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Roteiros",
						id: "routes",
						description: "Gerencie os roteiros de produção",
						requiredPermissions: [
							{
								action: "read",
								subject: "all",
							},
						],
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.ROTEIROS,
						visibleOnMobile: true,
						visibleOnMenu: true,
						icon: SlidersHorizontalIcon,
					},
					{
						name: "Setores",
						id: "sectors",
						description: "Gerencie os setores de produção",
						requiredPermissions: [
							{
								action: "read",
								subject: "all",
							},
						],
						route: PATHS_CONFIG.PRODUCAO.subPaths.CADASTROS.subPaths.SETORES,
						visibleOnMobile: true,
						visibleOnMenu: true,
						icon: SlidersHorizontalIcon,
					},
				],
			},
			{
				name: "Acompanhamento",
				id: "tracking",
				description: "Acompanhe o progresso da produção",
				requiredPermissions: [
					{
						action: "read",
						subject: "all",
					},
				],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.PRODUCAO.subPaths.ACOMPANHAMENTO,
				icon: SearchIcon,
			},
			{
				name: "Estatísticas",
				id: "statistics",
				description: "Visualize estatísticas de produção",
				requiredPermissions: [
					{
						action: "read",
						subject: "all",
					},
				],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.PRODUCAO.subPaths.ESTATISTICAS,
				icon: ChartNoAxesCombinedIcon,
			},
		],
	},
	{
		title: "Inspeção",
		items: [
			{
				id: "inspection-register",
				name: "Cadastros",
				description: "Defina formulários, medidas, campos e células de inspeção",
				requiredPermissions: [
					{
						action: "read",
						subject: "all",
					},
				],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.INSPECTION.subPaths.REGISTER,
				icon: SlidersHorizontalIcon,
			},
			// {
			// 	id: "inspection-management",
			// 	name: "Colaboradores",
			// 	description: "Gerencie os colaboradores de inspeção",
			// 	requiredPermissions: [
			// 		{
			// 			action: "read",
			// 			subject: "all",
			// 		},
			// 	],
			// 	visibleOnMobile: true,
			// 	visibleOnMenu: true,
			// 	route: PATHS_CONFIG.INSPECTION.subPaths.COLABORATORS,
			// 	icon: UsersIcon,
			// },
			// {
			// 	id: "inspection-query",
			// 	name: "Consultas",
			// 	description: "Consulte e analise dados de inspeção",
			// 	requiredPermissions: [
			// 		{
			// 			action: "read",
			// 			subject: "all",
			// 		},
			// 	],
			// 	visibleOnMobile: true,
			// 	visibleOnMenu: true,
			// 	route: PATHS_CONFIG.INSPECTION.subPaths.INSPECTION,
			// 	icon: SearchIcon,
			// },
		],
	},
];
