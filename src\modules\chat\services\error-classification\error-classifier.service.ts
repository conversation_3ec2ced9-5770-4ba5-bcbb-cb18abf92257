import { IChatError, IChatErrorContext, TChatErrorType, TChatErrorSeverity, TChatErrorRecoveryStrategy } from "../../types/handlers.type";

interface IClassifyErrorInput {
	readonly error: unknown;
	readonly context: Partial<IChatErrorContext>;
	readonly httpStatus?: number;
	readonly responseText?: string;
}

interface IErrorClassificationRule {
	readonly type: TChatErrorType;
	readonly severity: TChatErrorSeverity;
	readonly recoveryStrategy: TChatErrorRecoveryStrategy;
	readonly isRecoverable: boolean;
	readonly shouldRetry: boolean;
	readonly userMessage: string;
	readonly matcher: (input: IClassifyErrorInput) => boolean;
}

export class ChatErrorClassifierService {
	private readonly classificationRules: readonly IErrorClassificationRule[] = [
		{
			type: "network",
			severity: "medium",
			recoveryStrategy: "retry",
			isRecoverable: true,
			shouldRetry: true,
			userMessage: "Problema de conexão. Tentando reconectar...",
			matcher: (input) => this.isNetworkError(input),
		},
		{
			type: "authentication",
			severity: "high",
			recoveryStrategy: "refresh-token",
			isRecoverable: true,
			shouldRetry: false,
			userMessage: "Atualizando autenticação...",
			matcher: (input) => input.httpStatus === 401,
		},
		{
			type: "authorization",
			severity: "high",
			recoveryStrategy: "user-action",
			isRecoverable: false,
			shouldRetry: false,
			userMessage: "Acesso negado. Verifique suas permissões.",
			matcher: (input) => input.httpStatus === 403,
		},
		{
			type: "session-expired",
			severity: "medium",
			recoveryStrategy: "refresh-session",
			isRecoverable: true,
			shouldRetry: false,
			userMessage: "Sessão expirada. Renovando...",
			matcher: (input) => this.isSessionExpiredError(input),
		},
		{
			type: "timeout",
			severity: "medium",
			recoveryStrategy: "retry",
			isRecoverable: true,
			shouldRetry: true,
			userMessage: "Tempo limite excedido. Tentando novamente...",
			matcher: (input) => this.isTimeoutError(input),
		},
		{
			type: "rate-limit",
			severity: "low",
			recoveryStrategy: "retry",
			isRecoverable: true,
			shouldRetry: true,
			userMessage: "Muitas solicitações. Aguardando...",
			matcher: (input) => input.httpStatus === 429,
		},
		{
			type: "server-error",
			severity: "high",
			recoveryStrategy: "retry",
			isRecoverable: true,
			shouldRetry: true,
			userMessage: "Erro no servidor. Tentando novamente...",
			matcher: (input) => this.isServerError(input),
		},
		{
			type: "validation",
			severity: "low",
			recoveryStrategy: "user-action",
			isRecoverable: false,
			shouldRetry: false,
			userMessage: "Dados inválidos. Verifique sua mensagem.",
			matcher: (input) => input.httpStatus === 400,
		},
		{
			type: "generic",
			severity: "medium",
			recoveryStrategy: "none",
			isRecoverable: false,
			shouldRetry: false,
			userMessage: "Ocorreu um erro inesperado.",
			matcher: () => true, // Fallback rule
		},
	];

	public classify(input: IClassifyErrorInput): IChatError {
		const rule = this.findMatchingRule(input);
		const errorId = this.generateErrorId();
		const message = this.extractErrorMessage(input.error);
		
		const context: IChatErrorContext = {
			timestamp: Date.now(),
			httpStatus: input.httpStatus,
			endpoint: input.context.endpoint,
			sessionId: input.context.sessionId,
			requestId: input.context.requestId,
			userAgent: input.context.userAgent,
			retryAttempt: input.context.retryAttempt,
			maxRetries: input.context.maxRetries,
		};

		return {
			id: errorId,
			type: rule.type,
			severity: rule.severity,
			message,
			userMessage: rule.userMessage,
			recoveryStrategy: rule.recoveryStrategy,
			isRecoverable: rule.isRecoverable,
			shouldRetry: rule.shouldRetry,
			context,
			details: {
				originalError: input.error,
				stackTrace: input.error instanceof Error ? input.error.stack : undefined,
				responseData: input.responseText,
				networkInfo: {
					online: navigator.onLine,
					connectionType: this.getConnectionType(),
				},
			},
		};
	}

	private findMatchingRule(input: IClassifyErrorInput): IErrorClassificationRule {
		return this.classificationRules.find(rule => rule.matcher(input)) || this.classificationRules[this.classificationRules.length - 1];
	}

	private isNetworkError(input: IClassifyErrorInput): boolean {
		if (!navigator.onLine) return true;
		if (input.error instanceof TypeError && input.error.message.includes("fetch")) return true;
		if (input.error instanceof Error && input.error.name === "NetworkError") return true;
		return false;
	}

	private isSessionExpiredError(input: IClassifyErrorInput): boolean {
		if (input.httpStatus === 401) return false; // Authentication is different from session
		if (input.responseText?.includes("session") && input.responseText?.includes("expired")) return true;
		if (input.responseText?.includes("sessão") && input.responseText?.includes("expirada")) return true;
		return false;
	}

	private isTimeoutError(input: IClassifyErrorInput): boolean {
		if (input.error instanceof Error && input.error.name === "TimeoutError") return true;
		if (input.error instanceof Error && input.error.message.includes("timeout")) return true;
		if (input.httpStatus === 408) return true;
		return false;
	}

	private isServerError(input: IClassifyErrorInput): boolean {
		return input.httpStatus !== undefined && input.httpStatus >= 500 && input.httpStatus < 600;
	}

	private extractErrorMessage(error: unknown): string {
		if (error instanceof Error) return error.message;
		if (typeof error === "string") return error;
		return "Erro desconhecido";
	}

	private generateErrorId(): string {
		return `chat-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}

	private getConnectionType(): string | undefined {
		// @ts-expect-error - Navigator connection is experimental
		const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
		return connection?.effectiveType;
	}
}
