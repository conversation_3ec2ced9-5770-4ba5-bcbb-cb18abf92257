import { IChatError, IHandleErrorRecoveryScope, TChatErrorRecoveryStrategy } from "../../types/handlers.type";

export interface IAutoRecoveryResult {
	readonly success: boolean;
	readonly strategy: TChatErrorRecoveryStrategy;
	readonly message: string;
	readonly shouldRetryOriginalOperation: boolean;
	readonly error?: Error;
}

export interface ITokenRefreshScope {
	refreshToken(): Promise<void>;
	isTokenValid(): Promise<boolean>;
}

export interface ISessionRefreshScope {
	refreshSession(): Promise<void>;
	isSessionValid(): Promise<boolean>;
	createNewSession(): Promise<void>;
}

export interface IAutoRecoveryDependencies {
	readonly tokenService?: ITokenRefreshScope;
	readonly sessionService?: ISessionRefreshScope;
}

export class ChatAutoRecoveryService {
	constructor(private readonly dependencies: IAutoRecoveryDependencies) {}

	public async attemptRecovery(
		error: IChatError,
		recoveryScope?: IHandleErrorRecoveryScope
	): Promise<IAutoRecoveryResult> {
		try {
			switch (error.recoveryStrategy) {
				case "refresh-token":
					return await this.handleTokenRefresh(recoveryScope);

				case "refresh-session":
					return await this.handleSessionRefresh(recoveryScope);

				case "retry":
					return this.handleRetryStrategy();

				case "user-action":
					return await this.handleUserAction(error, recoveryScope);

				case "none":
				default:
					return this.handleNoRecovery(error);
			}
		} catch (recoveryError) {
			return {
				success: false,
				strategy: error.recoveryStrategy,
				message: "Falha na recuperação automática",
				shouldRetryOriginalOperation: false,
				error: recoveryError instanceof Error ? recoveryError : new Error("Erro desconhecido na recuperação"),
			};
		}
	}

	private async handleTokenRefresh(recoveryScope?: IHandleErrorRecoveryScope): Promise<IAutoRecoveryResult> {
		if (!this.dependencies.tokenService) {
			return {
				success: false,
				strategy: "refresh-token",
				message: "Serviço de token não disponível",
				shouldRetryOriginalOperation: false,
			};
		}

		try {
			// Check if token is still valid
			const isValid = await this.dependencies.tokenService.isTokenValid();
			if (isValid) {
				return {
					success: true,
					strategy: "refresh-token",
					message: "Token já válido",
					shouldRetryOriginalOperation: true,
				};
			}

			// Attempt token refresh
			if (recoveryScope?.onRefreshToken) {
				await recoveryScope.onRefreshToken();
			} else {
				await this.dependencies.tokenService.refreshToken();
			}

			// Verify token was refreshed successfully
			const isNowValid = await this.dependencies.tokenService.isTokenValid();
			if (!isNowValid) {
				throw new Error("Token refresh failed - token still invalid");
			}

			return {
				success: true,
				strategy: "refresh-token",
				message: "Token atualizado com sucesso",
				shouldRetryOriginalOperation: true,
			};
		} catch (error) {
			return {
				success: false,
				strategy: "refresh-token",
				message: "Falha ao atualizar token",
				shouldRetryOriginalOperation: false,
				error: error instanceof Error ? error : new Error("Erro desconhecido no refresh do token"),
			};
		}
	}

	private async handleSessionRefresh(recoveryScope?: IHandleErrorRecoveryScope): Promise<IAutoRecoveryResult> {
		if (!this.dependencies.sessionService) {
			return {
				success: false,
				strategy: "refresh-session",
				message: "Serviço de sessão não disponível",
				shouldRetryOriginalOperation: false,
			};
		}

		try {
			// Check if session is still valid
			const isValid = await this.dependencies.sessionService.isSessionValid();
			if (isValid) {
				return {
					success: true,
					strategy: "refresh-session",
					message: "Sessão já válida",
					shouldRetryOriginalOperation: true,
				};
			}

			// Attempt session refresh
			if (recoveryScope?.onRefreshSession) {
				await recoveryScope.onRefreshSession();
			} else {
				try {
					await this.dependencies.sessionService.refreshSession();
				} catch (refreshError) {
					// If refresh fails, try creating a new session
					console.warn("Session refresh failed, creating new session", refreshError);
					await this.dependencies.sessionService.createNewSession();
				}
			}

			// Verify session was refreshed/created successfully
			const isNowValid = await this.dependencies.sessionService.isSessionValid();
			if (!isNowValid) {
				throw new Error("Session recovery failed - session still invalid");
			}

			return {
				success: true,
				strategy: "refresh-session",
				message: "Sessão renovada com sucesso",
				shouldRetryOriginalOperation: true,
			};
		} catch (error) {
			return {
				success: false,
				strategy: "refresh-session",
				message: "Falha ao renovar sessão",
				shouldRetryOriginalOperation: false,
				error: error instanceof Error ? error : new Error("Erro desconhecido no refresh da sessão"),
			};
		}
	}

	private handleRetryStrategy(): IAutoRecoveryResult {
		return {
			success: true,
			strategy: "retry",
			message: "Tentando novamente",
			shouldRetryOriginalOperation: true,
		};
	}

	private async handleUserAction(
		error: IChatError,
		recoveryScope?: IHandleErrorRecoveryScope
	): Promise<IAutoRecoveryResult> {
		if (recoveryScope?.onUserAction) {
			try {
				await recoveryScope.onUserAction("default");
				return {
					success: true,
					strategy: "user-action",
					message: "Ação do usuário executada",
					shouldRetryOriginalOperation: true,
				};
			} catch (actionError) {
				return {
					success: false,
					strategy: "user-action",
					message: "Falha na ação do usuário",
					shouldRetryOriginalOperation: false,
					error: actionError instanceof Error ? actionError : new Error("Erro na ação do usuário"),
				};
			}
		}

		return {
			success: false,
			strategy: "user-action",
			message: error.userMessage,
			shouldRetryOriginalOperation: false,
		};
	}

	private handleNoRecovery(error: IChatError): IAutoRecoveryResult {
		return {
			success: false,
			strategy: "none",
			message: error.userMessage,
			shouldRetryOriginalOperation: false,
		};
	}

	public async isRecoveryPossible(error: IChatError): Promise<boolean> {
		if (!error.isRecoverable) return false;

		switch (error.recoveryStrategy) {
			case "refresh-token":
				return !!this.dependencies.tokenService;

			case "refresh-session":
				return !!this.dependencies.sessionService;

			case "retry":
				return error.shouldRetry;

			case "user-action":
			case "none":
			default:
				return false;
		}
	}
}
