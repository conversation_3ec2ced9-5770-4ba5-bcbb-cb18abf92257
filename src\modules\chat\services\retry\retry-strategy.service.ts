import { IChatError, TChatErrorType } from "../../types/handlers.type";

export interface IRetryConfiguration {
	readonly maxRetries: number;
	readonly baseDelay: number;
	readonly maxDelay: number;
	readonly backoffMultiplier: number;
	readonly jitterFactor: number;
}

export interface IRetryContext {
	readonly attempt: number;
	readonly totalAttempts: number;
	readonly lastError: IChatError;
	readonly startTime: number;
	readonly elapsedTime: number;
}

export interface IRetryResult<T> {
	readonly success: boolean;
	readonly data?: T;
	readonly finalError?: IChatError;
	readonly attempts: number;
	readonly totalTime: number;
}

export interface IRetryStrategyScope {
	shouldRetry(error: IChatError, context: IRetryContext): boolean;
	calculateDelay(context: IRetryContext): number;
	onRetryAttempt?(context: IRetryContext): void;
	onRetryExhausted?(context: IRetryContext): void;
}

export class ChatRetryStrategyService {
	private readonly defaultConfig: IRetryConfiguration = {
		maxRetries: 3,
		baseDelay: 1000,
		maxDelay: 30000,
		backoffMultiplier: 2,
		jitterFactor: 0.1,
	};

	private readonly errorTypeConfigs: Map<TChatErrorType, Partial<IRetryConfiguration>> = new Map([
		["network", { maxRetries: 5, baseDelay: 500, maxDelay: 10000 }],
		["timeout", { maxRetries: 3, baseDelay: 2000, maxDelay: 15000 }],
		["server-error", { maxRetries: 4, baseDelay: 1500, maxDelay: 20000 }],
		["rate-limit", { maxRetries: 6, baseDelay: 5000, maxDelay: 60000, backoffMultiplier: 1.5 }],
		["authentication", { maxRetries: 1, baseDelay: 0 }],
		["session-expired", { maxRetries: 1, baseDelay: 0 }],
		["authorization", { maxRetries: 0 }],
		["validation", { maxRetries: 0 }],
		["generic", { maxRetries: 2, baseDelay: 2000 }],
	]);

	public async executeWithRetry<T>(
		operation: () => Promise<T>,
		strategy: IRetryStrategyScope,
		customConfig?: Partial<IRetryConfiguration>
	): Promise<IRetryResult<T>> {
		const config = { ...this.defaultConfig, ...customConfig };
		const startTime = Date.now();
		let lastError: IChatError | undefined;
		let attempt = 0;

		while (attempt <= config.maxRetries) {
			try {
				const data = await operation();
				return {
					success: true,
					data,
					attempts: attempt + 1,
					totalTime: Date.now() - startTime,
				};
			} catch (error) {
				lastError = error as IChatError;
				attempt++;

				const context: IRetryContext = {
					attempt,
					totalAttempts: config.maxRetries + 1,
					lastError,
					startTime,
					elapsedTime: Date.now() - startTime,
				};

				if (attempt > config.maxRetries || !strategy.shouldRetry(lastError, context)) {
					strategy.onRetryExhausted?.(context);
					break;
				}

				strategy.onRetryAttempt?.(context);
				const delay = strategy.calculateDelay(context);
				await this.delay(delay);
			}
		}

		return {
			success: false,
			finalError: lastError,
			attempts: attempt,
			totalTime: Date.now() - startTime,
		};
	}

	public createStrategy(errorType: TChatErrorType): IRetryStrategyScope {
		const config = this.getConfigForErrorType(errorType);

		return {
			shouldRetry: (error: IChatError, context: IRetryContext) => {
				if (!error.shouldRetry || !error.isRecoverable) return false;
				if (context.attempt > config.maxRetries) return false;
				if (context.elapsedTime > 300000) return false; // 5 minutes max
				return true;
			},

			calculateDelay: (context: IRetryContext) => {
				const baseDelay = config.baseDelay;
				const exponentialDelay = baseDelay * Math.pow(config.backoffMultiplier, context.attempt - 1);
				const jitter = exponentialDelay * config.jitterFactor * Math.random();
				const delayWithJitter = exponentialDelay + jitter;
				
				return Math.min(delayWithJitter, config.maxDelay);
			},

			onRetryAttempt: (context: IRetryContext) => {
				console.debug(`Retry attempt ${context.attempt}/${context.totalAttempts} for error type: ${errorType}`, {
					error: context.lastError,
					elapsedTime: context.elapsedTime,
				});
			},

			onRetryExhausted: (context: IRetryContext) => {
				console.warn(`Retry exhausted after ${context.attempt} attempts for error type: ${errorType}`, {
					finalError: context.lastError,
					totalTime: context.elapsedTime,
				});
			},
		};
	}

	public createCustomStrategy(
		shouldRetryFn: (error: IChatError, context: IRetryContext) => boolean,
		calculateDelayFn: (context: IRetryContext) => number,
		config?: Partial<IRetryConfiguration>
	): IRetryStrategyScope {
		const finalConfig = { ...this.defaultConfig, ...config };

		return {
			shouldRetry: shouldRetryFn,
			calculateDelay: calculateDelayFn,
			onRetryAttempt: (context: IRetryContext) => {
				console.debug(`Custom retry attempt ${context.attempt}/${context.totalAttempts}`, context);
			},
		};
	}

	private getConfigForErrorType(errorType: TChatErrorType): IRetryConfiguration {
		const typeConfig = this.errorTypeConfigs.get(errorType) || {};
		return { ...this.defaultConfig, ...typeConfig };
	}

	private delay(ms: number): Promise<void> {
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}
