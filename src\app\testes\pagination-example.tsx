import React, { useState } from "react";
import { Pagination } from "../../shared/components/custom/pagination/pagination";

// Exemplo de como usar o componente Pagination melhorado
export const PaginationExample: React.FC = () => {
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(20);
	const [selectedItems, setSelectedItems] = useState<number[]>([]);

	// Dados de exemplo
	const totalItems = 250;
	const totalPages = Math.ceil(totalItems / pageSize);

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
		// Limpar seleções ao mudar de página (opcional)
		setSelectedItems([]);
	};

	const handlePageSizeChange = (newPageSize: number) => {
		setPageSize(newPageSize);
		// Recalcular página atual para manter aproximadamente os mesmos itens visíveis
		const newTotalPages = Math.ceil(totalItems / newPageSize);
		const newCurrentPage = Math.min(Math.ceil((currentPage * pageSize) / newPageSize), newTotalPages);
		setCurrentPage(newCurrentPage);
		setSelectedItems([]);
	};

	return (
		<div className="space-y-6 p-6">
			<h2 className="text-2xl font-bold">Exemplos do Componente de Paginação</h2>

			{/* Exemplo 1: Paginação básica */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Paginação Básica</h3>
				<div className="rounded-lg border p-4">
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						pageSize={pageSize}
						totalItems={totalItems}
						onPageChange={handlePageChange}
						onPageSizeChange={handlePageSizeChange}
					/>
				</div>
			</div>

			{/* Exemplo 2: Com itens selecionados */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Com Itens Selecionados</h3>
				<div className="rounded-lg border p-4">
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						pageSize={pageSize}
						totalItems={totalItems}
						selectedCount={selectedItems.length}
						onPageChange={handlePageChange}
						onPageSizeChange={handlePageSizeChange}
						showSelectedInfo={true}
					/>
				</div>
			</div>

			{/* Exemplo 3: Sem seletor de tamanho de página */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Sem Seletor de Tamanho</h3>
				<div className="rounded-lg border p-4">
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						pageSize={pageSize}
						totalItems={totalItems}
						onPageChange={handlePageChange}
						onPageSizeChange={handlePageSizeChange}
						showPageSizeSelector={false}
					/>
				</div>
			</div>

			{/* Exemplo 4: Poucas páginas */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Poucas Páginas (3 total)</h3>
				<div className="rounded-lg border p-4">
					<Pagination currentPage={1} totalPages={3} pageSize={20} totalItems={45} onPageChange={() => {}} onPageSizeChange={() => {}} />
				</div>
			</div>

			{/* Exemplo 5: Nenhum item */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Nenhum Item</h3>
				<div className="rounded-lg border p-4">
					<Pagination currentPage={1} totalPages={0} pageSize={20} totalItems={0} onPageChange={() => {}} onPageSizeChange={() => {}} />
				</div>
			</div>
		</div>
	);
};
