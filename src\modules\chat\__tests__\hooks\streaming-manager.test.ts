import { act, renderHook } from "@testing-library/react";
import { useStreamingManager } from "../../hooks/streaming-manager.hook";
import { sendStreamingMessage } from "../../lib/streaming-send-message.lib";
import { chatStreamService } from "../../services/streaming/streaming.service";

const mockUseAtomValue = jest.fn();
const mockUseSetAtom = jest.fn();

jest.mock("jotai", () => {
	const actual = jest.requireActual("jotai");
	return {
		...actual,
		useAtomValue: (...args: unknown[]) => mockUseAtomValue(...args),
		useSetAtom: (...args: unknown[]) => mockUseSetAtom(...args),
	};
});
jest.mock("../../lib/streaming-send-message.lib");
jest.mock("../../services/streaming/streaming.service");

beforeEach(() => {
	jest.clearAllMocks();
	mockUseAtomValue.mockReset();
	mockUseSetAtom.mockReset();
});

describe("useStreamingManager", () => {
	it("canSend, canStop, canResume refletem o estado", () => {
		mockUseAtomValue.mockReturnValueOnce("session-1").mockReturnValueOnce(false).mockReturnValueOnce("msg-id");
		mockUseSetAtom.mockReturnValue(jest.fn());
		const { result, rerender } = renderHook(() => useStreamingManager());
		expect(result.current.canSend).toBe(true);
		expect(result.current.canStop).toBe(false);
		expect(result.current.canResume).toBe(false);

		mockUseAtomValue.mockReturnValueOnce("session-1").mockReturnValueOnce(true).mockReturnValueOnce("msg-id");
		rerender();
		expect(result.current.canSend).toBe(false);
		expect(result.current.canStop).toBe(true);
		expect(result.current.canResume).toBe(true);

		mockUseAtomValue.mockReturnValueOnce(undefined).mockReturnValueOnce(false).mockReturnValueOnce(null);
		rerender();
		expect(result.current.canSend).toBe(false);
	});

	it("reset: chama resetStreaming e limpa erro", () => {
		mockUseAtomValue.mockReturnValueOnce("session-1");
		const resetStreamingMock = jest.fn();
		const setErrorMock = jest.fn();
		mockUseSetAtom
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(setErrorMock)
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(jest.fn())
			.mockReturnValue(resetStreamingMock);
		const { result } = renderHook(() => useStreamingManager());
		result.current.reset();
		expect(resetStreamingMock).toHaveBeenCalled();
		expect(setErrorMock).toHaveBeenCalledWith(null);
	});

	it("stop: aborta stream, chama stopStreaming e atualiza mensagem", () => {
		mockUseAtomValue.mockReturnValueOnce("session-1").mockReturnValueOnce(true).mockReturnValueOnce("msg-id");
		const updateMessageMock = jest.fn();
		const stopStreamingMock = jest.fn();
		mockUseSetAtom
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(updateMessageMock)
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(stopStreamingMock)
			.mockReturnValue(jest.fn());
		(chatStreamService.abortStream as jest.Mock) = jest.fn();
		const { result } = renderHook(() => useStreamingManager());
		result.current.stop();
		expect(chatStreamService.abortStream).toHaveBeenCalled();
		expect(stopStreamingMock).toHaveBeenCalled();
		expect(updateMessageMock).toHaveBeenCalledWith(
			"msg-id",
			expect.objectContaining({
				content: "Resposta interrompida pelo usuário.",
				isStreaming: false,
				isError: false,
			}),
		);
	});

	it("fluxo de erro: chama setError e stopStreaming ao ocorrer erro", async () => {
		mockUseAtomValue.mockReturnValueOnce("session-1").mockReturnValueOnce(false);
		const addMessageMock = jest.fn();
		const updateMessageMock = jest.fn();
		const setErrorMock = jest.fn();
		const startStreamingMock = jest.fn();
		const stopStreamingMock = jest.fn();
		mockUseSetAtom
			.mockReturnValueOnce(addMessageMock)
			.mockReturnValueOnce(updateMessageMock)
			.mockReturnValueOnce(setErrorMock)
			.mockReturnValueOnce(startStreamingMock)
			.mockReturnValueOnce(stopStreamingMock)
			.mockReturnValue(jest.fn());
		(sendStreamingMessage as jest.Mock).mockRejectedValue(new Error("Erro de envio"));
		const { result } = renderHook(() => useStreamingManager());
		await act(async () => {
			await result.current.sendMessage("mensagem");
		});
		expect(setErrorMock).toHaveBeenCalledWith("Erro de envio");
		expect(stopStreamingMock).toHaveBeenCalled();
	});

	it("fluxo de sucesso: chama startStreaming, sendStreamingMessage e stopStreaming", async () => {
		mockUseAtomValue.mockReturnValueOnce("session-1").mockReturnValueOnce(false);
		const addMessageMock = jest.fn();
		const updateMessageMock = jest.fn();
		const setErrorMock = jest.fn();
		const startStreamingMock = jest.fn();
		const stopStreamingMock = jest.fn();
		mockUseSetAtom
			.mockReturnValueOnce(addMessageMock)
			.mockReturnValueOnce(updateMessageMock)
			.mockReturnValueOnce(setErrorMock)
			.mockReturnValueOnce(startStreamingMock)
			.mockReturnValueOnce(stopStreamingMock)
			.mockReturnValue(jest.fn());
		(sendStreamingMessage as jest.Mock).mockImplementation(async ({ onStreamingStart }) => {
			onStreamingStart("msg-id");
		});
		const { result } = renderHook(() => useStreamingManager());
		await act(async () => {
			await result.current.sendMessage("mensagem");
		});
		expect(startStreamingMock).toHaveBeenCalledWith("msg-id");
		expect(sendStreamingMessage).toHaveBeenCalled();
		expect(stopStreamingMock).toHaveBeenCalled();
	});

	it("chama stopStreaming se já estiver transmitindo", async () => {
		mockUseAtomValue.mockReturnValueOnce("session-1").mockReturnValueOnce(true);
		const stopStreamingMock = jest.fn();
		mockUseSetAtom
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(jest.fn())
			.mockReturnValueOnce(stopStreamingMock)
			.mockReturnValue(jest.fn());
		(sendStreamingMessage as jest.Mock).mockResolvedValue(undefined);
		const { result } = renderHook(() => useStreamingManager());
		await act(async () => {
			await result.current.sendMessage("mensagem");
		});
		expect(stopStreamingMock).toHaveBeenCalled();
	});

	it("lança erro se não houver sessionId", async () => {
		mockUseAtomValue.mockReturnValueOnce(undefined);
		mockUseSetAtom.mockReturnValue(jest.fn());
		const { result } = renderHook(() => useStreamingManager());
		const promise = result.current.sendMessage("mensagem");
		await expect(promise).rejects.toThrow("O id da sessão não está disponível");
		expect(sendStreamingMessage).not.toHaveBeenCalled();
	});
});
