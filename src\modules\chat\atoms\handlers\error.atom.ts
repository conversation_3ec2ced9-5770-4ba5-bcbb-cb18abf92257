"use client";
import { atom } from "jotai";
import { IChatError } from "../../types/handlers.type";

export interface IChatErrorState {
	readonly error: IChatError | null;
	readonly isRecovering: boolean;
	readonly recoveryMessage: string | null;
	readonly showToUser: boolean;
	readonly retryCount: number;
}

export const chatErrorStateAtom = atom<IChatErrorState>({
	error: null,
	isRecovering: false,
	recoveryMessage: null,
	showToUser: false,
	retryCount: 0,
});

// Derived atoms for easier access
export const chatErrorAtom = atom(
	get => get(chatErrorStateAtom).error,
	(get, set, error: IChatError | null) => {
		const currentState = get(chatErrorStateAtom);
		set(chatErrorStateAtom, {
			...currentState,
			error,
			showToUser: error !== null && !error.isRecoverable,
			retryCount: error ? currentState.retryCount : 0,
		});
	},
);

export const chatIsRecoveringAtom = atom(
	get => get(chatErrorStateAtom).isRecovering,
	(get, set, isRecovering: boolean) => {
		const currentState = get(chatErrorStateAtom);
		set(chatErrorStateAtom, {
			...currentState,
			isRecovering,
		});
	},
);

export const chatRecoveryMessageAtom = atom(
	get => get(chatErrorStateAtom).recoveryMessage,
	(get, set, message: string | null) => {
		const currentState = get(chatErrorStateAtom);
		set(chatErrorStateAtom, {
			...currentState,
			recoveryMessage: message,
		});
	},
);

export const chatShouldShowErrorAtom = atom(get => {
	const state = get(chatErrorStateAtom);
	return state.showToUser && state.error !== null;
});

export const chatErrorDisplayMessageAtom = atom(get => {
	const state = get(chatErrorStateAtom);
	if (state.isRecovering && state.recoveryMessage) {
		return state.recoveryMessage;
	}
	return state.error?.userMessage || null;
});

export const incrementRetryCountAtom = atom(null, (get, set) => {
	const currentState = get(chatErrorStateAtom);
	set(chatErrorStateAtom, {
		...currentState,
		retryCount: currentState.retryCount + 1,
	});
});

export const resetErrorStateAtom = atom(null, (_get, set) => {
	set(chatErrorStateAtom, {
		error: null,
		isRecovering: false,
		recoveryMessage: null,
		showToUser: false,
		retryCount: 0,
	});
});
