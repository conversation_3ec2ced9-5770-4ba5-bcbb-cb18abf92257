/**
 * Componentes wrapper para erros em tabelas
 */

export { TableError, useTableError } from "./table-error";
export type { 
  // Exportar tipos se necessário no futuro
} from "./table-error";

// Re-export dos exemplos para desenvolvimento/documentação
export { TableErrorExample } from "./table-error.example";
export { UserTableWithErrorHandling, SimpleTableWithError } from "./table-error.integration";