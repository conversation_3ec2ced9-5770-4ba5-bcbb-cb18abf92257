"use client";

import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useEffect } from "react";
import { createNewSessionAtom } from "../atoms/session/create.atom";
import { sessionIdAtom } from "../atoms/session/info.atom";
import { removeSession<PERSON>tom } from "../atoms/session/remove.atom";

export const useSessionManager = ({ isOpen }: { isOpen: boolean }) => {
	const sessionId = useAtomValue(sessionIdAtom);
	const createNewSession = useSetAtom(createNewSessionAtom);
	const removeSession = useSetAtom(removeSessionAtom);

	useEffect(() => {
		if (isOpen && !sessionId) createNewSession();
	}, [isOpen, sessionId, createNewSession]);

	return { createNewSession, removeSession };
};
