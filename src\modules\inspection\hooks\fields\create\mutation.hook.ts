import { toast } from "@/core/toast";
import { FIELDS_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICreateFieldsDTO } from "@/modules/inspection/types/fields/create-fields.dto";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export function useCreateFieldMutation() {
	const queryClient = useQueryClient();

	const createFieldsMutations = useMutation({
		mutationKey: inspectionKeys.fields.custom("create"),
		mutationFn: async (form: ICreateFieldsDTO) => {
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(FIELDS_ENDPOINTS.CREATE, form);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.fields.invalidateAll(queryClient),
	});
	return {
		createField: (form: ICreateFieldsDTO) =>
			toast.promise(createFieldsMutations.mutateAsync(form), {
				loading: "Criando campo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
}
