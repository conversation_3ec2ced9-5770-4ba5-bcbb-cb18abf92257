import { render, screen } from "@testing-library/react";
import { Chat } from "../../components";

jest.mock("../../components/sidebar/container", () => ({
	ChatSidebar: () => <div data-testid="chat-sidebar" />,
}));
jest.mock("../../components/trigger", () => ({
	ChatTrigger: ({ position, size }: { position: string; size: string }) => <div data-testid="chat-trigger" data-position={position} data-size={size} />,
}));

describe("Componente Chat", () => {
	it("renderiza ChatTrigger e ChatSidebar por padrão", () => {
		render(<Chat />);
		expect(screen.getByTestId("chat-trigger")).toBeInTheDocument();
		expect(screen.getByTestId("chat-sidebar")).toBeInTheDocument();
	});

	it("não renderiza ChatTrigger quando showTrigger é false", () => {
		render(<Chat showTrigger={false} />);
		expect(screen.queryByTestId("chat-trigger")).not.toBeInTheDocument();
		expect(screen.getByTestId("chat-sidebar")).toBeInTheDocument();
	});

	it("passa as props triggerPosition e triggerSize para ChatTrigger", () => {
		render(<Chat triggerPosition="top-left" triggerSize="lg" />);
		const trigger = screen.getByTestId("chat-trigger");
		expect(trigger).toHaveAttribute("data-position", "top-left");
		expect(trigger).toHaveAttribute("data-size", "lg");
	});

	it("usa props padrão quando não fornecidas", () => {
		render(<Chat />);
		const trigger = screen.getByTestId("chat-trigger");
		expect(trigger).toHaveAttribute("data-position", "bottom-right");
		expect(trigger).toHaveAttribute("data-size", "md");
	});
});
