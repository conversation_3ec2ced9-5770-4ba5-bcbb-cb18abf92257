"use client";
import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { MessageSquare } from "lucide-react";
import { useEffect, useRef } from "react";
import { chatIsOpenAtom } from "../../atoms/controls/trigger.atom";
import { chatStreamingMessagesAtom, isMessagesAvailableAtom } from "../../atoms/session/info.atom";
import { useSessionManager } from "../../hooks/session-manager.hook";
import { useStreamingManager } from "../../hooks/streaming-manager.hook";
import { ChatMessage } from "./content";
import { ChatHeader } from "./header";
import { ChatInput } from "./input";
import { ChatErrorDisplay } from "../error/error-display";

export const ChatSidebar = () => {
	const isOpen = useAtomValue(chatIsOpenAtom);
	useSessionManager({ isOpen });
	const { sendMessage, stop, isStreaming } = useStreamingManager();
	const scrollAreaRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLTextAreaElement | null>(null);
	const hasMessages = useAtomValue(isMessagesAvailableAtom);
	const messages = useAtomValue(chatStreamingMessagesAtom);

	useEffect(() => {
		if (scrollAreaRef.current) scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
	}, [messages, isOpen]);

	useEffect(() => {
		if (isOpen) {
			// small timeout to ensure motion animation & mount are completed before focusing
			setTimeout(() => {
				inputRef.current?.focus();
			}, 100);
		}
	}, [isOpen]);

	return (
		<AnimatePresence>
			{isOpen && (
				<motion.div
					initial={{ opacity: 0, scale: 0.95, height: 0 }}
					animate={{ opacity: 1, scale: 1, height: 500 }}
					exit={{ opacity: 0, scale: 0.95, height: 0 }}
					transition={{ duration: 0.3, ease: "easeOut" }}
					className="rounded-main fixed right-[1rem] bottom-[6rem] flex w-[375px] max-w-[90%] touch-auto flex-col justify-between overflow-hidden border border-slate-200 bg-white shadow-2xl shadow-black/10 backdrop-blur-md sm:right-[4rem] sm:bottom-[7rem]"
				>
					<ChatHeader />
					<div ref={scrollAreaRef} className="relative flex-1 overflow-y-auto">
						{!hasMessages ? (
							<div className="flex h-full flex-col items-center justify-center p-8 text-center">
								<div className="bg-primary/10 dark:bg-primary/20 border-primary/20 dark:border-primary/30 mb-6 flex h-16 w-16 items-center justify-center rounded-full border shadow-sm transition-all duration-300 hover:scale-105">
									<MessageSquare className="text-primary h-8 w-8" />
								</div>
								<h3 className="mb-3 text-xl font-semibold text-slate-800 dark:text-slate-100">Bem-vindo à Doorinha</h3>
								<p className="max-w-sm text-sm leading-relaxed text-slate-600 dark:text-slate-400">
									Faça perguntas, peça ajuda ou converse sobre qualquer assunto. Estou aqui para ajudar!
								</p>
								<div className="mt-6 flex flex-wrap justify-center gap-2">
									<span className="bg-primary/10 dark:bg-primary/20 text-primary border-primary/20 rounded-full border px-3 py-1 text-xs font-medium">
										💡 Dicas
									</span>
									<span className="bg-primary/10 dark:bg-primary/20 text-primary border-primary/20 rounded-full border px-3 py-1 text-xs font-medium">
										🤝 Ajuda
									</span>
									<span className="bg-primary/10 dark:bg-primary/20 text-primary border-primary/20 rounded-full border px-3 py-1 text-xs font-medium">
										💬 Conversa
									</span>
								</div>
							</div>
						) : (
							<div className="space-y-2 p-4">
								{messages.map(message => (
									<ChatMessage key={message.id} message={message} />
								))}
							</div>
						)}
					</div>
					<ChatErrorDisplay
						onRetry={() => {
							// Retry last message if available
							console.log("Retry requested from error display");
						}}
						onDismiss={() => {
							// Reset error state
							console.log("Error dismissed");
						}}
					/>
					<ChatInput
						ref={inputRef}
						onSend={sendMessage}
						onStop={stop}
						disabled={false}
						placeholder="Digite sua dúvida..."
						isStreaming={isStreaming}
					/>
				</motion.div>
			)}
		</AnimatePresence>
	);
};
