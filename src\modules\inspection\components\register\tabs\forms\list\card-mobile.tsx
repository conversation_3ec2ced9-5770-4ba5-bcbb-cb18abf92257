import { Badge } from "@/shared/components/shadcn/badge";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Card, CardContent } from "@/shared/components/shadcn/card";
import { Separator } from "@/shared/components/shadcn/separator";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Copy, Eye, Link, Trash } from "lucide-react";
import { IFormDto } from "../../../../../types/forms/dtos/find-all.dto";
import { ConfirmCloneFormModal } from "../clone/confirm-modal";
import { ConfirmDeleteFormModal } from "../delete/confirm-modal";
import { ModalEditForm } from "../edit/modal";

interface IFormCardMobileProps {
	form: IFormDto;
	index: number;
}

type ModalKey = "edit" | "clone" | "delete";

const actions: { icon: React.ElementType; title: string; color: string; modal: <PERSON><PERSON><PERSON><PERSON> }[] = [
	{ icon: Eye, title: "Editar", color: "text-primary bg-primary/10", modal: "edit" },
	{ icon: Copy, title: "Clonar", color: "text-primary bg-primary/10", modal: "clone" },
	{ icon: Trash, title: "Excluir", color: "text-red-500 bg-red-500/10", modal: "delete" },
];

export const FormCardMobile = ({ form }: IFormCardMobileProps) => {
	const modals: Record<ModalKey, ReturnType<typeof useModal>> = {
		edit: useModal(),
		clone: useModal(),
		delete: useModal(),
	};

	return (
		<>
			<Card className="relative border bg-white shadow-sm hover:shadow-md">
				<CardContent>
					<div className="mb-3 flex items-start justify-between">
						<h3 className="mr-2 line-clamp-2 flex-1 text-base leading-tight font-semibold">
							{form.title}
							{!form.canUpdate && (
								<Badge variant="secondary" className="ml-2 text-xs">
									<Link />
								</Badge>
							)}
						</h3>
						<div className="bg-primary h-1.5 w-1.5 rounded-full opacity-60" />
					</div>
					<div className="mb-4 space-y-2">
						<div className="flex items-center justify-between">
							<span className="text-xs text-gray-500">Nomenclatura</span>
							<Badge variant="outline" className="bg-gray-50 px-2 py-0.5 font-mono text-xs text-gray-700">
								{form.nomenclature}
							</Badge>
						</div>
						<div className="flex items-center justify-between">
							<span className="text-xs text-gray-500">Revisão</span>
							<span className="bg-primary flex h-5 w-5 items-center justify-center rounded-full text-xs font-medium text-white">
								{form.revision}
							</span>
						</div>
					</div>
					<Separator className="my-4" />
					<div className="flex gap-1.5">
						{actions.map(({ icon: Icon, title, color, modal }, idx) => (
							<Button
								key={idx}
								size="sm"
								variant="outline"
								onClick={modals[modal].openModal}
								className={`h-8 flex-1 border-none px-2 text-xs font-medium ${color}`}
							>
								<Icon className="h-3 w-3" />
								<span>{modal === "edit" ? (form.canUpdate ? "Editar" : "Visualizar") : title}</span>
							</Button>
						))}
					</div>
				</CardContent>
			</Card>
			<ModalEditForm isOpen={modals.edit.isOpen} onClose={modals.edit.closeModal} formId={form.id} canEdit={form.canUpdate} />
			<ConfirmCloneFormModal isOpen={modals.clone.isOpen} onClose={modals.clone.closeModal} title={form.title} formId={form.id} />
			<ConfirmDeleteFormModal isOpen={modals.delete.isOpen} onClose={modals.delete.closeModal} title={form.title} formId={form.id} />
		</>
	);
};
