import { ChatRetryStrategyService } from "../../services/retry/retry-strategy.service";
import { IChatError } from "../../types/handlers.type";

describe("ChatRetryStrategyService", () => {
	let retryService: ChatRetryStrategyService;

	beforeEach(() => {
		retryService = new ChatRetryStrategyService();
	});

	describe("executeWithRetry", () => {
		it("should succeed on first attempt", async () => {
			const operation = jest.fn().mockResolvedValue("success");
			const strategy = retryService.createStrategy("network");

			const result = await retryService.executeWithRetry(operation, strategy);

			expect(result.success).toBe(true);
			expect(result.data).toBe("success");
			expect(result.attempts).toBe(1);
			expect(operation).toHaveBeenCalledTimes(1);
		});

		it("should retry on recoverable error", async () => {
			const operation = jest
				.fn()
				.mockRejectedValueOnce(createMockError("network", true, true))
				.mockResolvedValue("success");

			const strategy = retryService.createStrategy("network");

			const result = await retryService.executeWithRetry(operation, strategy);

			expect(result.success).toBe(true);
			expect(result.data).toBe("success");
			expect(result.attempts).toBe(2);
			expect(operation).toHaveBeenCalledTimes(2);
		});

		it("should not retry on non-recoverable error", async () => {
			const operation = jest.fn().mockRejectedValue(createMockError("authorization", false, false));

			const strategy = retryService.createStrategy("authorization");

			const result = await retryService.executeWithRetry(operation, strategy);

			expect(result.success).toBe(false);
			expect(result.attempts).toBe(1);
			expect(operation).toHaveBeenCalledTimes(1);
		});

		it("should exhaust retries for persistent errors", async () => {
			const error = createMockError("network", true, true);
			const operation = jest.fn().mockRejectedValue(error);

			const strategy = retryService.createStrategy("network");

			const result = await retryService.executeWithRetry(operation, strategy, { maxRetries: 2 });

			expect(result.success).toBe(false);
			expect(result.attempts).toBe(3); // Initial attempt + 2 retries
			expect(result.finalError).toBe(error);
			expect(operation).toHaveBeenCalledTimes(3);
		});
	});

	describe("createStrategy", () => {
		it("should create network strategy with correct config", () => {
			const strategy = retryService.createStrategy("network");

			const mockError = createMockError("network", true, true);
			const context = createMockContext(1, mockError);

			expect(strategy.shouldRetry(mockError, context)).toBe(true);
			expect(strategy.calculateDelay(context)).toBeGreaterThan(0);
		});

		it("should create authentication strategy with no retries", () => {
			const strategy = retryService.createStrategy("authentication");

			const mockError = createMockError("authentication", true, false);
			const context = createMockContext(1, mockError);

			expect(strategy.shouldRetry(mockError, context)).toBe(false);
		});

		it("should calculate exponential backoff delay", () => {
			const strategy = retryService.createStrategy("network");

			const context1 = createMockContext(1, createMockError("network", true, true));
			const context2 = createMockContext(2, createMockError("network", true, true));
			const context3 = createMockContext(3, createMockError("network", true, true));

			const delay1 = strategy.calculateDelay(context1);
			const delay2 = strategy.calculateDelay(context2);
			const delay3 = strategy.calculateDelay(context3);

			expect(delay2).toBeGreaterThan(delay1);
			expect(delay3).toBeGreaterThan(delay2);
		});

		it("should respect max delay limit", () => {
			const strategy = retryService.createStrategy("network");

			const context = createMockContext(10, createMockError("network", true, true));
			const delay = strategy.calculateDelay(context);

			expect(delay).toBeLessThanOrEqual(10000); // Max delay for network errors
		});
	});

	describe("createCustomStrategy", () => {
		it("should create custom strategy with provided functions", () => {
			const shouldRetryFn = jest.fn().mockReturnValue(true);
			const calculateDelayFn = jest.fn().mockReturnValue(1000);

			const strategy = retryService.createCustomStrategy(shouldRetryFn, calculateDelayFn);

			const mockError = createMockError("generic", true, true);
			const context = createMockContext(1, mockError);

			expect(strategy.shouldRetry(mockError, context)).toBe(true);
			expect(strategy.calculateDelay(context)).toBe(1000);
			expect(shouldRetryFn).toHaveBeenCalledWith(mockError, context);
			expect(calculateDelayFn).toHaveBeenCalledWith(context);
		});
	});

	describe("Error Type Configurations", () => {
		it("should have different retry limits for different error types", () => {
			const networkStrategy = retryService.createStrategy("network");
			const serverErrorStrategy = retryService.createStrategy("server-error");
			const rateLimitStrategy = retryService.createStrategy("rate-limit");

			const networkError = createMockError("network", true, true);
			const serverError = createMockError("server-error", true, true);
			const rateLimitError = createMockError("rate-limit", true, true);

			// Test that different strategies have different behaviors
			const networkContext = createMockContext(6, networkError); // Beyond network max retries
			const serverContext = createMockContext(5, serverError); // Beyond server max retries
			const rateLimitContext = createMockContext(7, rateLimitError); // Within rate limit max retries

			expect(networkStrategy.shouldRetry(networkError, networkContext)).toBe(false);
			expect(serverErrorStrategy.shouldRetry(serverError, serverContext)).toBe(false);
			expect(rateLimitStrategy.shouldRetry(rateLimitError, rateLimitContext)).toBe(false);
		});
	});
});

// Helper functions
function createMockError(type: IChatError["type"], isRecoverable: boolean, shouldRetry: boolean): IChatError {
	return {
		id: "test-error",
		type,
		severity: "medium",
		message: "Test error",
		userMessage: "Test user message",
		recoveryStrategy: "retry",
		isRecoverable,
		shouldRetry,
		context: {
			timestamp: Date.now(),
		},
	};
}

function createMockContext(attempt: number, lastError: IChatError) {
	return {
		attempt,
		totalAttempts: 5,
		lastError,
		startTime: Date.now() - 1000,
		elapsedTime: 1000,
	};
}
